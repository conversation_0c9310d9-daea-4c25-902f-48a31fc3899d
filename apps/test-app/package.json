{"name": "test-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@polyatomic/mypackage": "workspace:*", "@polyatomic/ui": "workspace:*", "@tldraw/sync": "^3.12.1", "next": "15.3.1", "react": "^19.1.0", "react-dom": "^19.1.0", "tldraw": "^3.12.1"}, "devDependencies": {"@polyatomic/eslint-config": "workspace:*", "@polyatomic/typescript-config": "workspace:*", "@tailwindcss/postcss": "^4", "@types/node": "^22.14.0", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "eslint": "^9.25.0", "tailwindcss": "^4", "typescript": "5.8.2"}}
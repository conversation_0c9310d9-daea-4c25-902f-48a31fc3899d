"use client";

import { test } from "@polyatomic/mypackage";
import { Tldraw } from "tldraw";
import { useSyncDemo } from "@tldraw/sync";
import { useState, useCallback } from "react";
import "tldraw/tldraw.css";

export default function Page() {
  const store = useSyncDemo({ roomId: "tldraw-test-app" });
  const store2 = useSyncDemo({ roomId: "tldraw-test-app2" });
  const [position, setPosition] = useState({ x: 200, y: 200 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      setIsDragging(true);
      setDragStart({
        x: e.clientX - position.x,
        y: e.clientY - position.y,
      });
    },
    [position]
  );

  const handleMouseMove = useCallback(
    (e: React.MouseEvent) => {
      if (!isDragging) return;
      setPosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y,
      });
    },
    [isDragging, dragStart]
  );

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  return (
    <div>
      <div
        style={{
          position: "fixed",
          inset: 0,
          width: "100%",
          height: "100%",
          marginLeft: "auto",
          marginRight: "auto",
        }}
      >
        <Tldraw store={store} />
      </div>
      <div
        id="tldraw-test-app2"
        style={{
          position: "fixed",
          width: "40%",
          height: "40%",
          left: position.x,
          top: position.y,
          border: "1px solid #ccc",
          borderRadius: "4px",
          overflow: "hidden",
        }}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        <div
          style={{
            height: "24px",
            background: "#f0f0f0",
            borderBottom: "1px solid #ccc",
            cursor: "move",
            display: "flex",
            alignItems: "center",
            padding: "0 8px",
          }}
          onMouseDown={handleMouseDown}
        >
          <span style={{ fontSize: "12px", color: "#666" }}>Drag to move</span>
        </div>
        <div style={{ width: "100%", height: "calc(100% - 24px)" }}>
          <Tldraw store={store2} />
        </div>
      </div>
    </div>
  );
}

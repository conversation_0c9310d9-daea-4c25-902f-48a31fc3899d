{"name": "collab-server", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "collab-server", "version": "1.0.0", "license": "ISC", "dependencies": {"@hocuspocus/server": "^3.1.1"}}, "node_modules/@hocuspocus/common": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/@hocuspocus/common/-/common-3.1.1.tgz", "integrity": "sha512-KtDZ8hHfwwjmamVTac6UTnH8nBf6Au33J4yiOxu/jW04MeFuhJQVvyfvO4AasV+/IA3nwHBAfR63X27bgm5yqw==", "dependencies": {"lib0": "^0.2.87"}}, "node_modules/@hocuspocus/server": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/@hocuspocus/server/-/server-3.1.1.tgz", "integrity": "sha512-JiOOeDqGrRCu2m9CfXRFilHts7HE9iw7z+M9cKPamI1n4LAaT1+mvrehAXPTZtIQ22WgRz5ui7Uf8fRJwn5qwA==", "dependencies": {"@hocuspocus/common": "^3.1.1", "async-lock": "^1.3.1", "kleur": "^4.1.4", "lib0": "^0.2.47", "uuid": "^11.0.3", "ws": "^8.5.0"}, "peerDependencies": {"y-protocols": "^1.0.6", "yjs": "^13.6.8"}}, "node_modules/async-lock": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/async-lock/-/async-lock-1.4.1.tgz", "integrity": "sha512-Az2ZTpuytrtqENulXwO3GGv1Bztugx6TT37NIo7imr/Qo0gsYiGtSdBa2B6fsXhTpVZDNfu1Qn3pk531e3q+nQ=="}, "node_modules/isomorphic.js": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/isomorphic.js/-/isomorphic.js-0.2.5.tgz", "integrity": "sha512-PIeMbHqMt4DnUP3MA/Flc0HElYjMXArsw1qwJZcm9sqR8mq3l8NYizFMty0pWwE/tzIGH3EKK5+jes5mAr85yw==", "funding": {"type": "GitHub Sponsors ❤", "url": "https://github.com/sponsors/dmonad"}}, "node_modules/kleur": {"version": "4.1.5", "resolved": "https://registry.npmjs.org/kleur/-/kleur-4.1.5.tgz", "integrity": "sha512-o+NO+8WrRiQEE4/7nwRJhN1HWpVmJm511pBHUxPLtp0BUISzlBplORYSmTclCnJvQq2tKu/sgl3xVpkc7ZWuQQ==", "engines": {"node": ">=6"}}, "node_modules/lib0": {"version": "0.2.107", "resolved": "https://registry.npmjs.org/lib0/-/lib0-0.2.107.tgz", "integrity": "sha512-2xih/AugT0dJSgeSfsW/bqIPILlsqzEtmw8hXzWEnMLrOz12DTK5z9rjNgUT21/HkBjHSznOQBr67bcZdc8Ltg==", "dependencies": {"isomorphic.js": "^0.2.4"}, "bin": {"0ecdsa-generate-keypair": "bin/0ecdsa-generate-keypair.js", "0gentesthtml": "bin/gentesthtml.js", "0serve": "bin/0serve.js"}, "engines": {"node": ">=16"}, "funding": {"type": "GitHub Sponsors ❤", "url": "https://github.com/sponsors/dmonad"}}, "node_modules/uuid": {"version": "11.1.0", "resolved": "https://registry.npmjs.org/uuid/-/uuid-11.1.0.tgz", "integrity": "sha512-0/A9rDy9P7cJ+8w1c9WD9V//9Wj15Ce2MPz8Ri6032usz+NfePxx5AcN3bN+r6ZL6jEo066/yNYB3tn4pQEx+A==", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "bin": {"uuid": "dist/esm/bin/uuid"}}, "node_modules/ws": {"version": "8.18.2", "resolved": "https://registry.npmjs.org/ws/-/ws-8.18.2.tgz", "integrity": "sha512-DMricUmwGZUVr++AEAe2uiVM7UoO9MAVZMDu05UQOaUII0lp+zOzLLU4Xqh/JvTqklB1T4uELaaPBKyjE1r4fQ==", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/y-protocols": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/y-protocols/-/y-protocols-1.0.6.tgz", "integrity": "sha512-vHRF2L6iT3rwj1jub/K5tYcTT/mEYDUppgNPXwp8fmLpui9f7Yeq3OEtTLVF012j39QnV+KEQpNqoN7CWU7Y9Q==", "peer": true, "dependencies": {"lib0": "^0.2.85"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "funding": {"type": "GitHub Sponsors ❤", "url": "https://github.com/sponsors/dmonad"}, "peerDependencies": {"yjs": "^13.0.0"}}, "node_modules/yjs": {"version": "13.6.26", "resolved": "https://registry.npmjs.org/yjs/-/yjs-13.6.26.tgz", "integrity": "sha512-wiARO3wixu7mtoRP5f7LqpUtsURP9SmNgXUt3RlnZg4qDuF7dUjthwIvwxIDmK55dPw4Wl4QdW5A3ag0atwu7g==", "peer": true, "dependencies": {"lib0": "^0.2.99"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "funding": {"type": "GitHub Sponsors ❤", "url": "https://github.com/sponsors/dmonad"}}}}
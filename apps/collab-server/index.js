import { Server } from "@hocuspocus/server";

// Track debounce timers for each document
const debounceTimers = new Map();

const server = new Server({
	port: 1234,
	timeout: 30000,

	onConnect: async ({ documentName, socketId, request }) => {
		const ip = request?.socket?.remoteAddress;
		const userAgent = request?.headers["user-agent"];

		console.log("\n------------\n");
		console.log(`🌐 IP: ${ip} \n`);
		console.log(`📄 Document: ${documentName} \n`);
		console.log(`📱 User-Agent: ${userAgent} \n`);
		console.log(`🧩 Socket: ${socketId} \n`);
	},

	onAuthenticate: async ({ documentName }) => {
		console.log("\n------------\n");
		console.log(`🔑 Authenticated connection to: ${documentName}\n`);
	},

	onDisconnect: async ({ documentName, socketId, request }) => {
		const ip = request?.socket?.remoteAddress;
		const userAgent = request?.headers["user-agent"];

		console.log("\n------------\n");
		console.log(`from: ${documentName} (socket: ${socketId})`);
		console.log(`❌ Disconnected IP: ${ip} -> ${documentName}`);
		console.log(`📱 User-Agent: ${userAgent}`);
		console.log(`🧩 Socket: ${socketId}`);
	},

	onDestroy: async ({ documentName }) => {
		console.log(`\n Document ${documentName} is being destroyed`);
	},

	onChange: async ({ documentName, document }) => {
		if (debounceTimers.has(documentName)) {
			clearTimeout(debounceTimers.get(documentName));
		}

		const timeout = setTimeout(() => {
			const tasks = document.getArray("tasks");
			console.log("\n------------\n");
			console.log(
				`✏️  Document "${documentName}" changed. \n   Current tasks:`,
				tasks.toArray() + "\n"
			);
			console.log(
				`#️⃣  Number of documents: ${server.hocuspocus.getDocumentsCount()}\n`
			);
			console.log(
				`👥 Number of connections: ${server.hocuspocus.getConnectionsCount()}\n`
			);
			debounceTimers.delete(documentName);
		}, 500);

		debounceTimers.set(documentName, timeout);
	},
});

server.listen();

setInterval(() => {
	const docCount = server.hocuspocus.getDocumentsCount();
	const connCount = server.hocuspocus.getConnectionsCount();
	console.log("------------");
	console.log(`📊 Active docs: ${docCount} | Connections: ${connCount}`);
}, 5000);

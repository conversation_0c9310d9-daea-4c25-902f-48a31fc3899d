"use client";

import Link from "next/link";
import { cn } from "@/lib/utils";

interface LogoProps {
	className?: string;
	size?: "sm" | "md" | "lg";
	showText?: boolean;
	href?: string;
	darkMode?: boolean;
}

export default function Logo({
	className,
	size = "md",
	showText = true,
	href = "/",
	darkMode,
}: LogoProps) {
	const sizes = {
		sm: 24,
		md: 32,
		lg: 48,
	};

	const logoSize = sizes[size];
	const textSize =
		size === "sm" ? "text-sm" : size === "md" ? "text-lg" : "text-xl";

	const LogoContent = () => (
		<div className={cn("flex items-center space-x-3", className)}>
			<div className="relative flex items-center justify-center">
				<svg
					xmlns="http://www.w3.org/2000/svg"
					viewBox="0 0 100 40"
					width={logoSize * 2}
					height={logoSize}
					className={cn(
						"text-black dark:text-white",
						darkMode ? "text-white" : "text-black"
					)}
				>
					<g>
						{/* Left circle */}
						<circle cx="15" cy="20" r="15" fill="currentColor" />
						{/* Right circle */}
						<circle cx="35" cy="20" r="15" fill="currentColor" />
						{/* Middle lines */}
						<g>
							<rect x="20" y="12" width="10" height="2" rx="1" fill="white" />
							<rect x="20" y="16" width="10" height="2" rx="1" fill="white" />
							<rect x="20" y="20" width="10" height="2" rx="1" fill="white" />
							<rect x="20" y="24" width="10" height="2" rx="1" fill="white" />
							<rect x="20" y="28" width="10" height="2" rx="1" fill="white" />
						</g>
					</g>
				</svg>
			</div>
			{showText && (
				<span
					className={cn(
						"font-semibold",
						textSize,
						darkMode ? "text-white" : "text-gray-900"
					)}
				>
					BudsCollab
				</span>
			)}
		</div>
	);

	if (href) {
		return (
			<Link href={href} className="flex items-center">
				<LogoContent />
			</Link>
		);
	}

	return <LogoContent />;
}

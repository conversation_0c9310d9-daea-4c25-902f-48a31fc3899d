import React, { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";

import { useTheme } from "next-themes";

export default function AppLogo({
  className = "",
  size = 32,
  alt = "Logo",
  scale = 0.9,
}) {
  const { resolvedTheme } = useTheme();
  const [logoSrc, setLogoSrc] = useState("/logo.png");

  useEffect(() => {
    if (resolvedTheme === "dark") {
      setLogoSrc("/logo-dark.png");
    } else {
      setLogoSrc("/logo.png");
    }
  }, [resolvedTheme]);

  return (
    <Link href="/" aria-label="Home">
      <span
        style={{
          display: "inline-block",
          transform: `scale(${scale})`,
          transformOrigin: "center",
          transition: "transform 0.2s cubic-bezier(.4,2,.6,1)",
          marginTop: "6px",
        }}
        tabIndex={0}
        className={className}
        onMouseEnter={(e) =>
          (e.currentTarget.style.transform = `scale(${scale * 1.15})`)
        }
        onMouseLeave={(e) =>
          (e.currentTarget.style.transform = `scale(${scale})`)
        }
        onFocus={(e) =>
          (e.currentTarget.style.transform = `scale(${scale * 1.15})`)
        }
        onBlur={(e) => (e.currentTarget.style.transform = `scale(${scale})`)}
      >
        <Image
          src={logoSrc}
          width={size}
          height={size}
          alt={alt}
          priority
          style={{ width: size, height: "auto", display: "block" }}
        />
      </span>
    </Link>
  );
}

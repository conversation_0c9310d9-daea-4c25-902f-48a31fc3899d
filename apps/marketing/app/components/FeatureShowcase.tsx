'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import { Users, Calendar, Database, MessageSquare } from 'lucide-react'

const features = [
  {
    icon: <Users className="w-12 h-12 text-primary dark:text-primary-light" />,
    title: 'Collaborative Rooms',
    description: 'Create dedicated spaces for projects, teams, or clients. Share resources, tasks, and discussions in one centralized location.',
    image: '/placeholder.svg?height=300&width=500'
  },
  {
    icon: <Calendar className="w-12 h-12 text-primary dark:text-primary-light" />,
    title: 'Real-time Task Management',
    description: 'Assign, track, and update tasks in real-time. Use Kanban boards, Gantt charts, or list views to visualize project progress.',
    image: '/placeholder.svg?height=300&width=500'
  },
  {
    icon: <Database className="w-12 h-12 text-primary dark:text-primary-light" />,
    title: 'Centralized Knowledge Base',
    description: 'Store and organize documents, notes, and resources. Use powerful search and tagging features to find information quickly.',
    image: '/placeholder.svg?height=300&width=500'
  },
  {
    icon: <MessageSquare className="w-12 h-12 text-primary dark:text-primary-light" />,
    title: 'Integrated Communication',
    description: 'Chat, video call, and collaborate in real-time. Keep all project-related communications in one place.',
    image: '/placeholder.svg?height=300&width=500'
  }
]

const FeatureShowcase = () => {
  return (
    <section className="py-20 bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-bold text-center mb-12">
          Discover <span className="text-primary dark:text-primary-light">BudsCollab</span> Features
        </h2>
        <div className="space-y-20">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className={`flex flex-col ${index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'} items-center gap-8`}
            >
              <div className="md:w-1/2">
                <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
                  <div className="mb-4">{feature.icon}</div>
                  <h3 className="text-2xl font-semibold mb-2">{feature.title}</h3>
                  <p className="text-gray-600 dark:text-gray-300">{feature.description}</p>
                </div>
              </div>
              <div className="md:w-1/2">
                <Image
                  src={feature.image}
                  alt={feature.title}
                  width={500}
                  height={300}
                  className="rounded-lg shadow-md"
                />
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default FeatureShowcase


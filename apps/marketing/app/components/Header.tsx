"use client";

import Link from "next/link";
import { motion } from "framer-motion";
import { useState, useEffect, useRef } from "react";
import { useTheme } from "next-themes";
import {
	Sun,
	Moon,
	ChevronDown,
	Zap,
	Users2,
	ListTodo,
	LayoutGrid,
	Database,
	Bot,
} from "lucide-react";
import AppLogo from "./AppLogo";

const features = [
	{
		icon: <Zap className="w-6 h-6 text-black dark:text-white" />,
		title: "Real-time Collaboration",
		href: "/features/real-time-collaboration",
	},
	{
		icon: <Users2 className="w-6 h-6 text-black dark:text-white" />,
		title: "Relationship Management",
		href: "/features/relationship-management",
	},
	{
		icon: <ListTodo className="w-6 h-6 text-black dark:text-white" />,
		title: "Task Tracking",
		href: "/features/task-tracking",
	},
	{
		icon: <LayoutGrid className="w-6 h-6 text-black dark:text-white" />,
		title: "Customizable Rooms",
		href: "/features/customizable-rooms",
	},
	{
		icon: <Database className="w-6 h-6 text-black dark:text-white" />,
		title: "Data Integration",
		href: "/features/data-integration",
	},
	{
		icon: <Bot className="w-6 h-6 text-black dark:text-white" />,
		title: "AI Assistant",
		href: "/features/ai-assistant",
	},
];

interface HeaderProps {
	isHomePage?: boolean;
}

const Header: React.FC<HeaderProps> = ({ isHomePage = false }) => {
	const [isOpen, setIsOpen] = useState(false);
	const [isFeatureDropdownOpen, setIsFeatureDropdownOpen] = useState(false);
	const [mounted, setMounted] = useState(false);
	const dropdownRef = useRef(null);
	const { theme, setTheme } = useTheme();

	useEffect(() => {
		setMounted(true);
	}, []);

	const handleDropdownKeyDown = (e: React.KeyboardEvent) => {
		if (e.key === "Enter" || e.key === " ") {
			setIsFeatureDropdownOpen(!isFeatureDropdownOpen);
		}
	};

	return (
		<header className="fixed w-full z-50 flex justify-center top-4">
  {/* Modern glassy background */}
			<div className="relative mx-4 w-full max-w-[800px]">
				<div className="absolute inset-0 bg-white/60 dark:bg-gray-900/60 backdrop-blur-xl shadow-2xl border border-gray-200 dark:border-gray-700 ring-1 ring-black/10 dark:ring-white/10 rounded-2xl" />
				<div className="relative flex h-16 items-center justify-between px-6 gap-2 md:gap-6">
					<div className="flex items-center">
						<AppLogo className="py-1 drop-shadow-lg" size={48} scale={1} alt="BudsCollab Logo" />
					</div>

					<div className="hidden md:flex items-center gap-3">
						<div
							className="relative mr-2"
							ref={dropdownRef}
							onMouseEnter={() => setIsFeatureDropdownOpen(true)}
							onMouseLeave={() => setIsFeatureDropdownOpen(false)}
						>
							<button
								onKeyDown={handleDropdownKeyDown}
								className="px-4 py-2 text-sm font-semibold tracking-tight text-gray-700 dark:text-gray-200 bg-white/60 dark:bg-gray-900/60 hover:bg-primary/10 dark:hover:bg-primary/20 rounded-full shadow-sm transition-all flex items-center ring-1 ring-gray-200 dark:ring-gray-700 hover:ring-primary/40 dark:hover:ring-primary/40 focus:outline-none focus:ring-2 focus:ring-primary/60"
								aria-haspopup="true"
								aria-expanded={isFeatureDropdownOpen}
							>
								Features
								<ChevronDown className="ml-1 w-4 h-4" />
							</button>
							{isFeatureDropdownOpen && (
								<motion.div
									initial={{ opacity: 0, y: 10 }}
									animate={{ opacity: 1, y: 0 }}
									exit={{ opacity: 0, y: 10 }}
									transition={{ duration: 0.2 }}
									className="absolute top-full left-0 mt-2 w-[400px] bg-white dark:bg-gray-800 rounded-lg shadow-lg py-4 z-10"
								>
									<div className="grid grid-cols-2 gap-2 p-4">
										{features.map((feature, index) => (
											<Link
												key={index}
												href={feature.href}
												className="flex items-center gap-3 p-3 rounded-xl hover:bg-primary/10 dark:hover:bg-primary/20 transition-all shadow-sm"
											>
												<div className="flex-shrink-0 w-10 h-10 bg-primary/10 dark:bg-primary-light/10 rounded-full flex items-center justify-center">
													{feature.icon}
												</div>
												<div className="flex-grow">
													<h3 className="text-sm font-medium text-gray-900 dark:text-white">
														{feature.title}
													</h3>
												</div>
											</Link>
										))}
									</div>
								</motion.div>
							)}
						</div>
						<NavLink href="/changelog">Changelog</NavLink>
						<NavLink href="/pricing">Pricing</NavLink>
						<NavLink href="/contact">Contact</NavLink>
						<button
							onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
							className="p-2 rounded-full bg-white/70 dark:bg-gray-800/70 hover:bg-primary/10 dark:hover:bg-primary/20 shadow transition-all ml-2"
							aria-label="Toggle theme"
						>
							{mounted ? (
								theme === "dark" ? (
									<Sun className="w-5 h-5" />
								) : (
									<Moon className="w-5 h-5" />
								)
							) : (
								<div className="w-5 h-5" />
							)}
						</button>
						<div className="flex items-center space-x-2 ml-4">
							<Link
								href="/login"
								className="h-8 px-4 inline-flex items-center justify-center text-sm font-semibold bg-gradient-to-tr from-primary to-primary/80 hover:from-primary/80 hover:to-primary/60 text-white rounded-full shadow-md transition-all whitespace-nowrap"
							>
								Log in
								{isHomePage && (
									<span className="ml-2 h-4 px-1 flex items-center justify-center rounded bg-white/20 text-xs font-normal text-white/70">
										L
									</span>
								)}
							</Link>
							<Link
								href="/signup"
								className="h-8 px-4 inline-flex items-center justify-center text-sm font-semibold text-gray-700 hover:text-black dark:text-white/80 dark:hover:text-white rounded-full bg-white/80 dark:bg-gray-800/80 shadow-md transition-all whitespace-nowrap ring-1 ring-gray-200 dark:ring-gray-700 hover:ring-primary/40 dark:hover:ring-primary/40"
							>
								Sign up
							</Link>
						</div>
					</div>

					<button
						onClick={() => setIsOpen(!isOpen)}
						className="md:hidden p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"
					>
						<svg
							className="w-6 h-6"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
						>
							<path
								strokeLinecap="round"
								strokeLinejoin="round"
								strokeWidth={2}
								d={isOpen ? "M6 18L18 6M6 6l12 12" : "M4 6h16M4 12h16M4 18h16"}
							/>
						</svg>
					</button>
				</div>
			</div>

			{/* Mobile menu */}
			{isOpen && (
				<motion.div
					initial={{ opacity: 0, y: -20 }}
					animate={{ opacity: 1, y: 0 }}
					exit={{ opacity: 0, y: -20 }}
					className="absolute top-20 left-4 right-4 bg-white dark:bg-gray-900 rounded-2xl border border-gray-200 dark:border-gray-700 shadow-lg backdrop-blur-lg"
				>
					<div className="flex flex-col space-y-1 p-4">
						<div className="mb-4">
							<h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-2">
								Features
							</h3>
							<div className="grid grid-cols-2 gap-2">
								{features.map((feature, index) => (
									<Link
										key={index}
										href={feature.href}
										className="flex items-center space-x-2 p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
									>
										<div className="flex-shrink-0 w-8 h-8 bg-primary/10 dark:bg-primary-light/10 rounded-full flex items-center justify-center">
											{feature.icon}
										</div>
										<span className="text-sm text-gray-700 dark:text-gray-200">
											{feature.title}
										</span>
									</Link>
								))}
							</div>
						</div>
						<MobileNavLink href="/changelog">Changelog</MobileNavLink>
						<MobileNavLink href="/pricing">Pricing</MobileNavLink>
						<MobileNavLink href="/contact">Contact</MobileNavLink>
						<div className="pt-2 mt-2 border-t border-gray-200 dark:border-gray-700">
							<div className="flex space-x-2">
								<Link
									href="/login"
									className="flex-1 h-9 inline-flex items-center justify-center text-sm font-medium bg-black hover:bg-gray-900 dark:bg-white/10 dark:hover:bg-white/20 text-white dark:text-white/90 rounded-[4px] transition-colors"
								>
									Log in
								</Link>
								<Link
									href="/signup"
									className="flex-1 h-9 inline-flex items-center justify-center text-sm font-medium text-gray-700 hover:text-black dark:text-white/70 dark:hover:text-white rounded-lg transition-colors"
								>
									Sign up
								</Link>
							</div>
						</div>
					</div>
				</motion.div>
			)}
		</header>
	);
};

interface NavLinkProps {
	href: string;
	children: React.ReactNode;
}

const NavLink: React.FC<NavLinkProps> = ({ href, children }) => (
	<Link
		href={href}
		className="px-3 py-2 text-sm font-medium text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white rounded-full transition-colors"
	>
		{children}
	</Link>
);

const MobileNavLink: React.FC<NavLinkProps> = ({ href, children }) => (
	<Link
		href={href}
		className="block px-4 py-2 text-base font-medium text-gray-700 dark:text-gray-200 hover:text-gray-900 dark:hover:text-white"
	>
		{children}
	</Link>
);

export default Header;

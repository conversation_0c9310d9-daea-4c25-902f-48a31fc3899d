"use client";

import { motion } from "framer-motion";
import {
	ArrowRight,
	Network,
	HelpCircle,
	Calendar,
	FileText,
	Folder,
	MessageCircle,
	ListTodo,
	Users,
	Sun,
	Plus,
	LayoutGrid,
	Globe,
	Send,
} from "lucide-react";
import Image from "next/image";

import { <PERSON><PERSON> } from "@/app/components/ui/button";
import React from "react";

// Helper function to generate diverse avatars
const getAvatarUrl = (seed: string) => {
	// Create a deterministic index based on the seed string
	const hashCode = seed.split("").reduce((acc, char) => {
		return char.charCodeAt(0) + ((acc << 5) - acc);
	}, 0);

	const baseColors = [
		"FF6B6B", // Red
		"4ECDC4", // Teal
		"FFD93D", // Yellow
		"95E1D3", // Mint
		"6C5CE7", // Purple
		"A8E6CF", // Light Green
		"FF9F7D", // Coral
		"7EB6E7", // Sky Blue
	];

	// Use the hashCode to consistently select colors and variations
	const colorIndex = Math.abs(hashCode) % baseColors.length;
	const color = baseColors[colorIndex];

	// Create URL with correct Micah parameters
	return `https://api.dicebear.com/6.x/micah/svg?seed=${seed}&backgroundColor=${color}`;
};

const Hero = () => {
	const [isInputFocused, setIsInputFocused] = React.useState(false);
	const [showCommands, setShowCommands] = React.useState(false);
	const [isExpanded, setIsExpanded] = React.useState(false);
	const inputRef = React.useRef<HTMLInputElement>(null);
	const [inputValue, setInputValue] = React.useState("");
	const [selectedCommandIndex, setSelectedCommandIndex] = React.useState(0);
	const [lastSlashTime, setLastSlashTime] = React.useState(0);

	const commands = [
		{
			icon: <Sun className="w-4 h-4 text-amber-500" />,
			label: "Idea",
			value: "idea",
			color: "text-amber-500",
		},
		{
			icon: <HelpCircle className="w-4 h-4 text-blue-500" />,
			label: "Question",
			value: "question",
			color: "text-blue-500",
		},
		{
			icon: <LayoutGrid className="w-4 h-4 text-purple-500" />,
			label: "Task",
			value: "task",
			color: "text-purple-500",
		},
		{
			icon: <Calendar className="w-4 h-4 text-green-500" />,
			label: "Event",
			value: "event",
			color: "text-green-500",
		},
	];

	const filteredCommands = commands.filter((command) =>
		command.label
			.toLowerCase()
			.includes(inputValue.toLowerCase().replace("/", ""))
	);

	React.useEffect(() => {
		const handleKeyPress = (e: KeyboardEvent) => {
			if (e.key === "/" && !isInputFocused) {
				e.preventDefault();
				inputRef.current?.focus();
				setIsExpanded(true);
			}
		};

		document.addEventListener("keydown", handleKeyPress);
		return () => document.removeEventListener("keydown", handleKeyPress);
	}, [isInputFocused]);

	const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
		if (e.key === "/" && e.target instanceof HTMLInputElement) {
			e.preventDefault();
			const now = Date.now();
			if (now - lastSlashTime < 500) {
				// Double slash within 500ms
				setShowCommands(true);
				setIsExpanded(true);
				setInputValue("");
			}
			setLastSlashTime(now);
		}

		if (!showCommands) return;

		switch (e.key) {
			case "ArrowDown":
				e.preventDefault();
				setSelectedCommandIndex((prev) =>
					prev < filteredCommands.length - 1 ? prev + 1 : prev
				);
				break;
			case "ArrowUp":
				e.preventDefault();
				setSelectedCommandIndex((prev) => (prev > 0 ? prev - 1 : prev));
				break;
			case "Enter":
				e.preventDefault();
				if (filteredCommands[selectedCommandIndex]) {
					setInputValue(filteredCommands[selectedCommandIndex].label);
					setShowCommands(false);
				}
				break;
			case "Escape":
				setShowCommands(false);
				break;
		}
	};

	const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const value = e.target.value;
		setInputValue(value);
		setSelectedCommandIndex(0);

		if (value === "/") {
			const now = Date.now();
			if (now - lastSlashTime < 500) {
				// Double slash within 500ms
				setShowCommands(true);
				setIsExpanded(true);
				setInputValue("");
			}
			setLastSlashTime(now);
		} else if (value.startsWith("/")) {
			setShowCommands(true);
		} else {
			setShowCommands(false); // Only show commands with slash or double slash
		}
	};

	const handleBlur = () => {
		setIsInputFocused(false);
		setTimeout(() => {
			setShowCommands(false);
			if (!inputRef.current?.value) {
				setIsExpanded(false);
			}
		}, 200);
	};

	return (
		<div className="relative min-h-screen bg-white dark:bg-gray-900 text-gray-900 dark:text-white transition-colors duration-200">
			{/* Dreamy Gradient Background */}
			<div className="absolute inset-0 overflow-hidden">
				{/* Base gradient */}
				<div className="absolute inset-0 bg-gradient-to-br from-white via-gray-50 to-rose-50/20 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800" />

				{/* Soft clouds/blobs effect - more subtle */}
				<div className="absolute inset-0">
					<div className="absolute top-1/4 right-1/3 w-[800px] h-[800px] bg-rose-100/20 rounded-full mix-blend-multiply filter blur-3xl animate-blob-slow" />
					<div className="absolute -bottom-32 left-1/3 w-[600px] h-[600px] bg-purple-100/20 rounded-full mix-blend-multiply filter blur-3xl animate-blob-slow animation-delay-4000" />
				</div>

				{/* Subtle overlay */}
				<div className="absolute inset-0 bg-white/50 dark:bg-gray-900/50" />
			</div>

			{/* Add the animation classes to the style section */}
			<style jsx>{`
				@keyframes blob-slow {
					0% {
						transform: translate(0px, 0px) scale(1);
					}
					33% {
						transform: translate(20px, -20px) scale(1.05);
					}
					66% {
						transform: translate(-10px, 10px) scale(0.95);
					}
					100% {
						transform: translate(0px, 0px) scale(1);
					}
				}
				.animate-blob-slow {
					animation: blob-slow 15s infinite linear;
				}
				.animation-delay-4000 {
					animation-delay: 4s;
				}
			`}</style>

			{/* Main Content */}
			<div className="relative pt-24">
				<div className="container mx-auto px-4 pb-12">
					{/* Headline */}
					<motion.div
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ delay: 0.1 }}
						className="text-center max-w-4xl mx-auto mb-8"
					>
						<h1 className="text-5xl md:text-7xl font-bold mb-6">
							<span className="block mb-2">Cultivate Connections,</span>
							<span className="bg-clip-text text-transparent bg-gradient-to-r from-gray-900 via-purple-500 to-pink-500 dark:from-white dark:via-purple-400 dark:to-pink-500">
								Amplify Achievements
							</span>
						</h1>
						<p className="text-xl md:text-2xl text-gray-700 dark:text-gray-300 mb-8">
							BudsCollab: Where relationships flourish and projects thrive. Join
							a vibrant community designed for personal growth and shared
							success.
						</p>
						<div className="flex flex-col sm:flex-row items-center justify-center gap-4">
							<motion.a
								href="#"
								whileHover={{ scale: 1.02 }}
								whileTap={{ scale: 0.98 }}
								className="inline-flex items-center px-8 py-3 rounded-full bg-gray-900 dark:bg-white text-white dark:text-gray-900 font-semibold text-lg group transition-colors"
							>
								Start your journey
								<ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
							</motion.a>
							<motion.a
								href="#"
								whileHover={{ scale: 1.02 }}
								whileTap={{ scale: 0.98 }}
								className="inline-flex items-center px-8 py-3 rounded-full bg-white dark:bg-gray-800 text-gray-900 dark:text-white font-semibold text-lg border border-gray-200 dark:border-gray-700 transition-colors"
							>
								See it in action
							</motion.a>
						</div>
					</motion.div>

					{/* Enhanced Interactive Preview */}
					<motion.div
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ delay: 0.2 }}
						className="max-w-5xl mx-auto mt-8"
					>
						<h2 className="text-2xl md:text-3xl font-bold mb-6 text-center">
							Smart Team Rooms with AI-Powered Assistance
						</h2>
						<div className="rounded-2xl bg-white dark:bg-gray-800 shadow-xl dark:shadow-2xl border border-gray-200 dark:border-gray-700 p-8">
							<div className="grid grid-cols-1 md:grid-cols-2 gap-8">
								{/* Rooms Preview */}
								<div className="bg-gray-100 dark:bg-gray-900 rounded-2xl p-6 shadow-lg">
									<h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
										<Users className="w-6 h-6 mr-2 text-black dark:text-white" />
										Collaborative Rooms
									</h3>
									<div className="space-y-6">
										{[
											{
												name: "Product Launch Team",
												members: ["Alex", "Jamie"],
												stats: [
													{
														icon: (
															<HelpCircle className="w-4 h-4 text-yellow-500 dark:text-yellow-400" />
														),
														count: 3,
													},
													{
														icon: (
															<ListTodo className="w-4 h-4 text-black dark:text-white" />
														),
														count: 8,
													},
													{
														icon: (
															<Calendar className="w-4 h-4 text-green-500 dark:text-green-400" />
														),
														count: 2,
													},
													{
														icon: (
															<FileText className="w-4 h-4 text-purple-500 dark:text-purple-400" />
														),
														count: 5,
													},
													{
														icon: (
															<Folder className="w-4 h-4 text-orange-500 dark:text-orange-400" />
														),
														count: 7,
													},
													{
														icon: (
															<MessageCircle className="w-4 h-4 text-pink-500 dark:text-pink-400" />
														),
														count: 15,
													},
												],
											},
											{
												name: "Team Retreat Planning",
												members: ["Taylor", "Morgan", "Casey"],
												stats: [
													{
														icon: (
															<HelpCircle className="w-4 h-4 text-yellow-500 dark:text-yellow-400" />
														),
														count: 5,
													},
													{
														icon: (
															<ListTodo className="w-4 h-4 text-black dark:text-white" />
														),
														count: 12,
													},
													{
														icon: (
															<Calendar className="w-4 h-4 text-green-500 dark:text-green-400" />
														),
														count: 3,
													},
													{
														icon: (
															<FileText className="w-4 h-4 text-purple-500 dark:text-purple-400" />
														),
														count: 8,
													},
													{
														icon: (
															<Folder className="w-4 h-4 text-orange-500 dark:text-orange-400" />
														),
														count: 10,
													},
													{
														icon: (
															<MessageCircle className="w-4 h-4 text-pink-500 dark:text-pink-400" />
														),
														count: 25,
													},
												],
											},
										].map((room, index) => (
											<div key={index} className="space-y-3">
												<div className="flex items-center justify-between">
													<div className="flex items-center space-x-3">
														<div className="flex -space-x-2">
															{room.members.map((member, i) => (
																<Image
																	key={i}
																	src={getAvatarUrl(`${room.name}-${member}`)}
																	alt={member}
																	width={32}
																	height={32}
																	className="rounded-full border-2 border-white dark:border-gray-800"
																/>
															))}
														</div>
														<div>
															<h4 className="text-lg font-semibold text-gray-900 dark:text-white">
																{room.name}
															</h4>
															<p className="text-sm text-gray-600 dark:text-gray-400">
																{room.members.join(", ")}
															</p>
														</div>
													</div>
												</div>
												<div className="flex flex-wrap items-center gap-4">
													{room.stats.map((stat, i) => (
														<div
															key={i}
															className="flex items-center space-x-1"
														>
															{stat.icon}
															<span className="text-sm text-gray-600 dark:text-gray-400">
																{stat.count}
															</span>
														</div>
													))}
												</div>
											</div>
										))}
									</div>
								</div>

								{/* Chat Preview */}
								<div className="bg-gray-50 dark:bg-gray-800 rounded-2xl p-6 shadow-sm">
									<h3 className="font-semibold mb-4 text-gray-900 dark:text-white">
										BudsCollab Assistant
									</h3>
									<div className="space-y-4">
										<div className="flex items-start gap-3">
											<Image
												src={getAvatarUrl("buddy-assistant")}
												alt="Buddy"
												width={32}
												height={32}
												className="rounded-full"
											/>
											<div className="bg-white dark:bg-gray-700 rounded-lg p-3 text-sm text-gray-700 dark:text-gray-200">
												<p>
													Welcome to your collaborative space! How can I assist
													you today?
												</p>
											</div>
										</div>
										<div className="flex items-start gap-3 justify-end">
											<div className="bg-black dark:bg-white text-white dark:text-gray-900 rounded-2xl p-3 text-sm transition-colors duration-200">
												<p>I need help organizing our product launch tasks.</p>
											</div>
										</div>
										<div className="flex items-start gap-3">
											<Image
												src={getAvatarUrl("buddy-assistant")}
												alt="Buddy"
												width={32}
												height={32}
												className="rounded-full"
											/>
											<div className="bg-white dark:bg-gray-700 rounded-lg p-3 text-sm text-gray-700 dark:text-gray-200">
												<p>
													I can help you organize your product launch tasks
													efficiently. Let me know what you need or use commands
													for quick actions.
												</p>
											</div>
										</div>
										<div className="relative">
											<div
												className={`relative transition-all duration-200 ${
													isExpanded ? "w-full" : "w-48"
												}`}
											>
												<div
													className={`relative flex items-center transition-all duration-300 ease-in-out ${
														isExpanded
															? "bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 rounded-xl shadow-lg"
															: "bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm hover:bg-white/80 dark:hover:bg-gray-800/80 rounded-full shadow-md"
													}`}
												>
													<input
														ref={inputRef}
														type="text"
														value={inputValue}
														placeholder={
															isExpanded
																? "Type a message or press / for commands..."
																: "Press / to start..."
														}
														className={`w-full px-4 py-2.5 text-sm bg-transparent border-none focus:outline-none focus:ring-0 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-gray-100 ${
															isExpanded ? "" : "cursor-pointer"
														}`}
														onFocus={() => {
															setIsInputFocused(true);
															setIsExpanded(true);
														}}
														onBlur={handleBlur}
														onChange={handleInputChange}
														onKeyDown={handleKeyDown}
													/>
													{isExpanded && (
														<Button
															size="sm"
															className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 p-0 bg-gradient-to-r from-gray-900 via-purple-500 to-pink-500 dark:from-white dark:via-purple-400 dark:to-pink-500 text-white dark:text-gray-900 rounded-lg hover:opacity-90 transition-opacity"
														>
															<Send className="h-4 w-4" />
														</Button>
													)}
												</div>
												{showCommands && filteredCommands.length > 0 && (
													<div className="absolute w-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg max-h-[200px] overflow-y-auto z-50">
														<div className="p-2 text-sm font-medium text-gray-500 dark:text-gray-400">
															Quick Actions
														</div>
														<div className="p-1">
															{filteredCommands.map((command, index) => (
																<button
																	key={index}
																	className={`w-full flex items-center gap-2 px-2 py-1.5 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md ${
																		selectedCommandIndex === index
																			? "bg-gray-100 dark:bg-gray-700"
																			: ""
																	}`}
																	onMouseEnter={() =>
																		setSelectedCommandIndex(index)
																	}
																	onClick={() => {
																		setInputValue(command.label);
																		setShowCommands(false);
																	}}
																>
																	{command.icon}
																	<span className={command.color}>
																		{command.label}
																	</span>
																</button>
															))}
														</div>
													</div>
												)}
											</div>
										</div>
									</div>
								</div>
							</div>

							{/* Avatar Navigation Preview */}
							<div className="mt-8 flex justify-between items-center">
								<div className="flex -space-x-2">
									{[1, 2, 3, 4].map((i) => (
										<Image
											key={i}
											src={getAvatarUrl(`team-lead-${i}_v${i + 12}`)}
											alt={`Team Member ${i}`}
											width={40}
											height={40}
											className="rounded-full border-2 border-white dark:border-gray-800"
										/>
									))}
									<div className="w-10 h-10 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center border-2 border-white dark:border-gray-800">
										<Plus className="w-5 h-5" />
									</div>
								</div>
								<div className="flex space-x-2">
									{["🏠", "💼", "📚", "✨", "+"].map((emoji, i) => (
										<div
											key={i}
											className="w-8 h-8 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-sm"
										>
											{emoji}
										</div>
									))}
								</div>
							</div>
						</div>
					</motion.div>
				</div>
			</div>
		</div>
	);
};

export default Hero;

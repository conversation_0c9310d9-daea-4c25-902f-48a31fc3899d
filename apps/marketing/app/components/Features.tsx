"use client";

import { motion } from "framer-motion";
import { Users, Calendar, Database, Zap, ArrowUpRight } from "lucide-react";
import Image from "next/image";

const getAvatarUrl = (seed: string) => {
	// Create a deterministic index based on the seed string
	const hashCode = seed.split("").reduce((acc, char) => {
		return char.charCodeAt(0) + ((acc << 5) - acc);
	}, 0);

	const baseColors = [
		"FF6B6B", // Red
		"4ECDC4", // Teal
		"FFD93D", // Yellow
		"95E1D3", // Mint
		"6C5CE7", // Purple
		"A8E6CF", // Light Green
		"FF9F7D", // Coral
		"7EB6E7", // Sky Blue
	];

	// Use the hashCode to consistently select colors and variations
	const colorIndex = Math.abs(hashCode) % baseColors.length;
	const color = baseColors[colorIndex];

	// Create URL with correct Micah parameters
	return `https://api.dicebear.com/6.x/micah/svg?seed=${seed}&backgroundColor=${color}`;
};

const features = [
	{
		icon: <Users className="w-12 h-12 text-black dark:text-white" />,
		title: "Relationship-first",
		description: "Prioritize meaningful connections through shared spaces.",
		users: ["Alex_v1", "Jamie_v2", "Taylor_v3"],
		stats: { projects: 12, tasks: 48, meetings: 8 },
		color:
			"from-rose-400/20 to-pink-400/20 dark:from-rose-600/10 dark:to-pink-600/10",
		textColor: "text-rose-600 dark:text-rose-400",
	},
	{
		icon: <Calendar className="w-12 h-12 text-black dark:text-white" />,
		title: "Streamlined Management",
		description: "Modular tools for easier collaboration and task tracking.",
		users: ["Morgan_v4", "Casey_v5", "Jordan_v6"],
		stats: { projects: 8, tasks: 36, meetings: 5 },
		color:
			"from-purple-400/20 to-indigo-400/20 dark:from-purple-600/10 dark:to-indigo-600/10",
		textColor: "text-purple-600 dark:text-purple-400",
	},
	{
		icon: <Database className="w-12 h-12 text-black dark:text-white" />,
		title: "Centralized Data",
		description: "Consolidate notes, resources, and tasks in real-time.",
		users: ["Sam_v7", "Riley_v8", "Quinn_v9"],
		stats: { projects: 15, tasks: 64, meetings: 10 },
		color:
			"from-blue-400/20 to-cyan-400/20 dark:from-blue-600/10 dark:to-cyan-600/10",
		textColor: "text-blue-600 dark:text-blue-400",
	},
	{
		icon: <Zap className="w-12 h-12 text-black dark:text-white" />,
		title: "Scalable Innovation",
		description: "Cutting-edge technology for dynamic, scalable solutions.",
		users: ["Drew_v10", "Avery_v11", "Parker_v12"],
		stats: { projects: 10, tasks: 42, meetings: 6 },
		color:
			"from-amber-400/20 to-orange-400/20 dark:from-amber-600/10 dark:to-orange-600/10",
		textColor: "text-amber-600 dark:text-amber-400",
	},
];

const Features = () => {
	return (
		<section className="py-12 bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-800">
			<div className="container mx-auto px-4">
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					whileInView={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.5 }}
					viewport={{ once: true }}
					className="text-center mb-16"
				>
					<h2 className="text-4xl md:text-5xl font-bold mb-6">
						Why Choose{" "}
						<span className="relative">
							<span className="relative z-10 bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-pink-600 dark:from-purple-400 dark:to-pink-400">
								BudsCollab
							</span>
							<motion.span
								className="absolute inset-0 bg-gradient-to-r from-purple-100 to-pink-100 dark:from-purple-900/30 dark:to-pink-900/30 -rotate-2"
								initial={{ width: 0 }}
								whileInView={{ width: "100%" }}
								transition={{ duration: 0.5, delay: 0.2 }}
							/>
						</span>
						?
					</h2>
					<p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
						Experience a new era of collaboration where technology meets human
						connection
					</p>
				</motion.div>
				<div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-7xl mx-auto">
					{features.map((feature, index) => (
						<motion.div
							key={index}
							initial={{ opacity: 0, y: 20 }}
							whileInView={{ opacity: 1, y: 0 }}
							transition={{ duration: 0.5, delay: index * 0.1 }}
							viewport={{ once: true }}
							whileHover={{ y: -5 }}
							className="relative group"
						>
							<div
								className="absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl blur-xl -z-10"
								style={{
									background: `radial-gradient(circle at top left, var(--${feature.color}), transparent)`,
								}}
							/>
							<div className="bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 hover:shadow-2xl transition-all duration-300">
								<div className="flex items-start gap-6">
									<motion.div
										className={`flex-shrink-0 bg-gradient-to-br ${feature.color} p-4 rounded-xl`}
										whileHover={{ scale: 1.05 }}
										transition={{ type: "spring", stiffness: 300 }}
									>
										{feature.icon}
									</motion.div>
									<div className="flex-grow">
										<h3
											className={`text-2xl font-semibold mb-3 ${feature.textColor} flex items-center group-hover:gap-2`}
										>
											{feature.title}
											<ArrowUpRight className="w-5 h-5 opacity-0 group-hover:opacity-100 transition-opacity" />
										</h3>
										<p className="text-gray-600 dark:text-gray-300 text-lg mb-6">
											{feature.description}
										</p>
										<div className="space-y-4">
											<div className="flex items-center justify-between">
												<div className="flex items-center -space-x-2">
													{feature.users.map((user, i) => (
														<Image
															key={i}
															src={getAvatarUrl(`${feature.title}-${user}`)}
															alt={user}
															width={32}
															height={32}
															className="rounded-full border-2 border-white dark:border-gray-800"
														/>
													))}
													<div className="w-8 h-8 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-sm border-2 border-white dark:border-gray-800">
														+3
													</div>
												</div>
												<div className="flex gap-6 text-sm text-gray-600 dark:text-gray-400">
													<span>{feature.stats.projects} Projects</span>
													<span>{feature.stats.tasks} Tasks</span>
													<span>{feature.stats.meetings} Meetings</span>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</motion.div>
					))}
				</div>
			</div>
		</section>
	);
};

export default Features;

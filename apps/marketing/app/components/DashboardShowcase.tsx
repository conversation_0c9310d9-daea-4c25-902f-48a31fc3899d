"use client";

import React, { useState } from "react";
import { useTheme } from "next-themes";
import { motion, AnimatePresence, Reorder } from "framer-motion";
import Image from "next/image";
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
} from "@/app/components/ui/card";
import {
	Paintbrush,
	LayoutGrid,
	Sliders,
	Users,
	ArrowRight,
	Sparkles,
	Zap,
	Clock,
	Calendar,
	BarChart2,
	Globe,
	Sun,
	Plane,
	Moon,
	Palette,
	MessageSquare,
	Send,
	Bot,
	Lightbulb,
	FileText,
	ListTodo,
	Settings,
} from "lucide-react";
import { Button } from "@/app/components/ui/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@/app/components/ui/dropdown-menu";
import {
	Command,
	CommandDialog,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from "@/app/components/ui/command";

const getAvatarUrl = (seed: string) => {
	// Create a deterministic index based on the seed string
	const hashCode = seed.split("").reduce((acc, char) => {
		return char.charCodeAt(0) + ((acc << 5) - acc);
	}, 0);

	const baseColors = [
		"FF6B6B", // Red
		"4ECDC4", // Teal
		"FFD93D", // Yellow
		"95E1D3", // Mint
		"6C5CE7", // Purple
		"A8E6CF", // Light Green
		"FF9F7D", // Coral
		"7EB6E7", // Sky Blue
	];

	// Use the hashCode to consistently select colors and variations
	const colorIndex = Math.abs(hashCode) % baseColors.length;
	const color = baseColors[colorIndex];

	// Create URL with correct Micah parameters
	return `https://api.dicebear.com/6.x/micah/svg?seed=${seed}&backgroundColor=${color}`;
};

const features = [
	{
		icon: <LayoutGrid className="w-6 h-6 text-black dark:text-white" />,
		title: "Customizable Dashboard",
		description: "Arrange and resize widgets to create your perfect workspace.",
		activeUsers: ["Alex_v20", "Jamie_v21", "Taylor_v22"],
		activity: "Updated 2 minutes ago",
	},
	{
		icon: <Paintbrush className="w-6 h-6 text-black dark:text-white" />,
		title: "Personalized Themes",
		description:
			"Choose from various themes or create your own to match your style.",
		activeUsers: ["Morgan_v23", "Casey_v24"],
		activity: "Active now",
	},
	{
		icon: <Sliders className="w-6 h-6 text-black dark:text-white" />,
		title: "Intuitive Controls",
		description:
			"Easily manage your tasks, notes, and projects with user-friendly interfaces.",
		activeUsers: ["Sam_v25", "Riley_v26", "Quinn_v27", "Jordan_v28"],
		activity: "Updated 5 minutes ago",
	},
	{
		icon: <Users className="w-6 h-6 text-black dark:text-white" />,
		title: "Collaborative Workspaces",
		description: "Create and join team spaces for seamless collaboration.",
		activeUsers: ["Drew_v29", "Avery_v30"],
		activity: "Active now",
	},
];

const DashboardShowcase: React.FC = () => {
	// All hooks at the top (no early return before this)
	const [isInputFocused, setIsInputFocused] = React.useState(false);
	const [isCommandOpen, setIsCommandOpen] = React.useState(false);
	const [currentView, setCurrentView] = useState<
		| "main"
		| "addThought"
		| "settings"
		| "customizePill"
		| "notifications"
		| "autoSave"
		| "fontSize"
		| "widgetMarketplace"
	>("main");
	const inputRef = React.useRef<HTMLInputElement>(null);
	const boardRef = React.useRef<HTMLDivElement>(null);
	// ... (other hooks remain here, including theme, wallTheme, mounted, etc.)

	React.useEffect(() => {
		const handleKeyPress = (e: KeyboardEvent) => {
			// Only handle slash if we're not already in an input/textarea
			if (
				e.key === "/" &&
				!isInputFocused &&
				!(e.target instanceof HTMLInputElement) &&
				!(e.target instanceof HTMLTextAreaElement)
			) {
				e.preventDefault(); // Prevent the browser's find text behavior
				inputRef.current?.focus();
			}
		};

		document.addEventListener("keydown", handleKeyPress);
		return () => document.removeEventListener("keydown", handleKeyPress);
	}, [isInputFocused]);

	const actions = [
		{
			category: "Thoughts",
			items: [
				{
					id: "new-idea",
					name: "New Idea",
					shortcut: ["i"],
					icon: <Lightbulb className="w-4 h-4 text-yellow-500" />,
				},
				{
					id: "new-note",
					name: "New Note",
					shortcut: ["n"],
					icon: <FileText className="w-4 h-4 text-blue-500" />,
				},
				{
					id: "new-task",
					name: "New Task",
					shortcut: ["t"],
					icon: <ListTodo className="w-4 h-4 text-green-500" />,
				},
			],
		},
		{
			category: "Widgets",
			items: [
				{
					id: "add-chat",
					name: "Add Chat Widget",
					shortcut: ["w", "c"],
					icon: <MessageSquare className="w-4 h-4" />,
				},
				{
					id: "add-weather",
					name: "Add Weather Widget",
					shortcut: ["w", "w"],
					icon: <Sun className="w-4 h-4 text-yellow-500" />,
				},
				{
					id: "add-calendar",
					name: "Add Calendar Widget",
					shortcut: ["w", "d"],
					icon: <Calendar className="w-4 h-4" />,
				},
			],
		},
		{
			category: "Navigation",
			items: [
				{
					id: "go-settings",
					name: "Go to Settings",
					shortcut: ["g", "s"],
					icon: <Settings className="w-4 h-4" />,
				},
				{
					id: "go-marketplace",
					name: "Open Widget Marketplace",
					shortcut: ["g", "m"],
					icon: <LayoutGrid className="w-4 h-4" />,
				},
			],
		},
	];

	const handleCommandSelect = React.useCallback((id: string) => {
		setIsCommandOpen(false);
		// Handle different actions
		switch (id) {
			case "new-idea":
			case "new-note":
			case "new-task":
				setCurrentView("addThought");
				break;
			case "add-chat":
			case "add-weather":
			case "add-calendar":
				setCurrentView("widgetMarketplace");
				break;
			case "go-settings":
				setCurrentView("settings");
				break;
			case "go-marketplace":
				setCurrentView("widgetMarketplace");
				break;
		}
	}, []);

	const [widgets, setWidgets] = useState([
		{
			id: "chat",
			icon: <MessageSquare className="w-6 h-6" />,
			title: "BudsCollab Assistant",
			content: (
				<div className="space-y-4">
					<div className="flex flex-col space-y-3">
						<div className="flex items-start gap-2">
							<Image
								src={getAvatarUrl("buds-assistant")}
								alt="Assistant"
								width={32}
								height={32}
								className="rounded-full"
							/>
							<div className="flex-1">
								<div className="font-medium text-sm">Assistant</div>
								<div className="text-sm text-gray-600 dark:text-gray-300">
									Welcome to your collaborative space! How can I assist you
									today?
								</div>
							</div>
						</div>
						<div className="flex items-start gap-2">
							<Image
								src={getAvatarUrl("user-demo")}
								alt="User"
								width={32}
								height={32}
								className="rounded-full"
							/>
							<div className="flex-1">
								<div className="font-medium text-sm">You</div>
								<div className="text-sm text-gray-600 dark:text-gray-300">
									I need help organizing our product launch tasks.
								</div>
							</div>
						</div>
						<div className="flex items-start gap-2">
							<Image
								src={getAvatarUrl("buds-assistant")}
								alt="Assistant"
								width={32}
								height={32}
								className="rounded-full"
							/>
							<div className="flex-1">
								<div className="font-medium text-sm">Assistant</div>
								<div className="text-sm text-gray-600 dark:text-gray-300">
									I can help you organize your product launch tasks efficiently.
									Let me know what you need!
								</div>
							</div>
						</div>
					</div>
					<div className="relative">
						<div className="flex items-center gap-2 bg-gray-50 dark:bg-gray-900 rounded-lg p-2">
							<Image
								src={getAvatarUrl("user-demo")}
								alt="User"
								width={32}
								height={32}
								className="rounded-full"
							/>
							<div className="flex-1 relative">
								<input
									ref={inputRef}
									type="text"
									placeholder="Type / for commands..."
									className="w-full bg-transparent border-none focus:outline-none focus:ring-0 text-sm text-gray-900 dark:text-gray-100 placeholder-gray-500"
									onFocus={() => setIsInputFocused(true)}
									onBlur={() => {
										setIsInputFocused(false);
										setIsCommandOpen(false);
									}}
									onKeyDown={(e) => {
										if (e.key === "/" && isInputFocused) {
											e.preventDefault();
											setIsCommandOpen(true);
										}
									}}
									onChange={(e) => {
										const value = e.target.value;
										if (value.startsWith("/")) {
											setIsCommandOpen(true);
										} else {
											setIsCommandOpen(false);
										}
									}}
								/>
								{isCommandOpen && (
									<div className="absolute left-0 right-0 top-full mt-1 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden z-50">
										<div className="p-2 text-xs text-gray-500 dark:text-gray-400 border-b border-gray-200 dark:border-gray-700">
											Type to filter...
										</div>
										{actions.map((group) => (
											<div key={group.category}>
												<div className="px-2 py-1 text-xs font-medium text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-900/50">
													{group.category}
												</div>
												{group.items.map((action) => (
													<button
														key={action.id}
														onClick={() => {
															handleCommandSelect(action.id);
															setIsCommandOpen(false);
														}}
														className="w-full px-2 py-1.5 text-sm text-left flex items-center gap-2 hover:bg-gray-100 dark:hover:bg-gray-700/50"
													>
														{action.icon}
														<span className="flex-1">{action.name}</span>
														<kbd className="px-1.5 py-0.5 text-xs font-mono bg-gray-100 dark:bg-gray-800 rounded">
															{action.shortcut.join(" ")}
														</kbd>
													</button>
												))}
											</div>
										))}
									</div>
								)}
								<Send className="h-4 w-4" />
							</div>
						</div>
					</div>
				</div>
			),
			position: { x: 320, y: 40 },
		},
		{
			id: "weather",
			icon: <Sun className="w-6 h-6 text-yellow-500" />,
			title: "Weather",
			content: "72°F Sunny",
			position: { x: 320, y: 40 },
		},
		{
			id: "calendar",
			icon: <Calendar className="w-6 h-6" />,
			title: "Calendar",
			content: "3 events today",
			position: { x: 40, y: 240 },
		},
		{
			id: "chart",
			icon: <BarChart2 className="w-6 h-6" />,
			title: "Analytics",
			content: "+23% this week",
			position: { x: 320, y: 240 },
		},
		{
			id: "flights",
			icon: <Plane className="w-6 h-6" />,
			title: "Flights",
			content: "SFO → NYC $299",
			position: { x: 180, y: 140 },
		},
	]);

	const { resolvedTheme } = useTheme();
	const [wallTheme, setWallTheme] = useState<
		"light" | "dark" | "blue" | "green"
	>("light");
	const [mounted, setMounted] = useState(false);

	React.useEffect(() => {
		setMounted(true);
	}, []);

	React.useEffect(() => {
		if (!mounted) return;
		if (resolvedTheme === "dark" && wallTheme === "light") {
			setWallTheme("dark");
		} else if (resolvedTheme === "light" && wallTheme === "dark") {
			setWallTheme("light");
		}
	}, [resolvedTheme, mounted, wallTheme]);

	// Helper to render the wall area (skeleton or mounted)
	function renderWallArea() {
		if (!mounted) {
			return (
				<div className="relative h-[600px] rounded-2xl p-8 overflow-hidden shadow-xl border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800" />
			);
		}
		return (
			<div
				ref={boardRef}
				className="relative h-[600px] rounded-2xl p-8 overflow-hidden shadow-xl border border-gray-200 dark:border-gray-700"
				style={{ background: getWallStyles().background }}
			>
				<div className="absolute inset-0 pointer-events-none">
					<div
						className="absolute inset-0"
						style={{
							backgroundImage: `radial-gradient(${getWallStyles().dotColor} 1px, transparent 1px)`,
							backgroundSize: "24px 24px",
						}}
					/>
				</div>
				<div className="absolute top-4 right-4 z-50">
					<DropdownMenu>
						<DropdownMenuTrigger asChild>
							<Button
								variant="outline"
								size="icon"
								className={`${
									wallTheme === "dark"
										? "bg-gray-800 hover:bg-gray-700"
										: "bg-white hover:bg-gray-50"
								} border-gray-200 dark:border-gray-700`}
							>
								<Palette className="h-4 w-4" />
							</Button>
						</DropdownMenuTrigger>
						<DropdownMenuContent
							className={`${
								wallTheme === "dark"
									? "bg-gray-800 border-gray-700"
									: "bg-white border-gray-200"
							}`}
							align="end"
						>
							<DropdownMenuItem
								className="hover:bg-gray-100 dark:hover:bg-gray-700"
								onClick={() => setWallTheme("light")}
							>
								<Sun className="h-4 w-4 mr-2" />
								Light
							</DropdownMenuItem>
							<DropdownMenuItem
								className="hover:bg-gray-100 dark:hover:bg-gray-700"
								onClick={() => setWallTheme("dark")}
							>
								<Moon className="h-4 w-4 mr-2" />
								Dark
							</DropdownMenuItem>
							<DropdownMenuItem
								className="hover:bg-gray-100 dark:hover:bg-gray-700"
								onClick={() => setWallTheme("blue")}
							>
								<div className="h-4 w-4 mr-2 rounded-full bg-blue-500" />
								Blue
							</DropdownMenuItem>
							<DropdownMenuItem
								className="hover:bg-gray-100 dark:hover:bg-gray-700"
								onClick={() => setWallTheme("green")}
							>
								<div className="h-4 w-4 mr-2 rounded-full bg-green-500" />
								Green
							</DropdownMenuItem>
						</DropdownMenuContent>
					</DropdownMenu>
				</div>
				<div className="relative w-full h-full">
					{widgets.map((widget) => (
						<motion.div
							key={widget.id}
							drag
							dragMomentum={false}
							dragElastic={0}
							dragConstraints={boardRef}
							initial={{
								x: widget.position.x,
								y: widget.position.y,
							}}
							animate={{
								x: widget.position.x,
								y: widget.position.y,
							}}
							style={{
								position: "absolute",
								left: 0,
								top: 0,
								width: 250,
								touchAction: "none",
							}}
							onDragEnd={(_, info) => handleDragEnd(widget.id, info)}
							whileHover={{ scale: 1.02 }}
							whileDrag={{
								scale: 1.05,
								zIndex: 50,
							}}
							className="cursor-grab active:cursor-grabbing"
						>
							<Card
								className={`backdrop-blur-md ${
									wallTheme === "dark"
										? "bg-gray-900/80 border-gray-700"
										: wallTheme === "blue"
										? "bg-blue-100/80 border-blue-200"
										: wallTheme === "green"
										? "bg-green-100/80 border-green-200"
										: "bg-white/80 border-gray-200"
								} shadow-lg p-4 rounded-lg`}
							>
								<div className="flex items-center gap-2 mb-2">
									<span className="text-xl">
										{widget.icon}
									</span>
									<span className="font-semibold text-gray-900 dark:text-white">
										{widget.title}
									</span>
								</div>
								<div className="rounded-lg bg-gray-50/80 dark:bg-gray-900/60 px-3 py-2">
								{widget.content}
							</div>
							</Card>
						</motion.div>
					))}
				</div>
				<div
					className={`absolute bottom-4 right-4 z-40 px-3 py-1.5 rounded-full text-sm font-medium border flex items-center gap-2 ${
						wallTheme === "dark"
							? "bg-gray-800 border-gray-700 text-white"
							: "bg-white border-gray-200 text-gray-900"
					}`}
				>
					<Users className="w-4 h-4" />
					Team Wall
				</div>
			</div>
		);
	}

	const getWallStyles = () => {
		switch (wallTheme) {
			case "dark":
				return {
					background: "rgb(17 24 39)",
					dotColor: "rgba(255, 255, 255, 0.2)",
				};
			case "blue":
				return {
					background: "#EEF3FB",
					dotColor: "rgba(37, 99, 235, 0.2)",
				};
			case "green":
				return {
					background: "#ECFDF5",
					dotColor: "rgba(16, 185, 129, 0.2)",
				};
			default: // light
				return {
					background: "#ffffff",
					dotColor: "rgba(0, 0, 0, 0.1)",
				};
		}
	};

	const handleDragEnd = (id: string, info: any) => {
		const deltaX = info.offset.x;
		const deltaY = info.offset.y;

		setWidgets((prevWidgets) =>
			prevWidgets.map((widget) =>
				widget.id === id
					? {
							...widget,
							position: {
								x: widget.position.x + deltaX,
								y: widget.position.y + deltaY,
							},
					  }
					: widget
			)
		);
	};

	if (!mounted) return null;

	return (
		<section className="py-20 bg-gradient-to-b from-white to-gray-50 dark:from-gray-800 dark:to-gray-900">
			<div className="container mx-auto px-4">
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					whileInView={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.5 }}
					viewport={{ once: true }}
					className="text-center mb-16"
				>
					<span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-purple-50 dark:bg-purple-900/30 text-purple-600 dark:text-purple-300 mb-4">
						<Sparkles className="w-4 h-4 mr-2" />
						Powerful Features
					</span>
					<h2 className="text-4xl md:text-5xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-gray-900 via-purple-500 to-pink-500 dark:from-white dark:via-purple-400 dark:to-pink-500">
						Your Personalized Dashboard
					</h2>
					<p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
						Transform your workflow with a dashboard that adapts to your needs
					</p>
				</motion.div>

				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-24">
					{features.map((feature, index) => (
						<motion.div
							key={index}
							initial={{ opacity: 0, y: 20 }}
							whileInView={{ opacity: 1, y: 0 }}
							transition={{ duration: 0.5, delay: index * 0.1 }}
							viewport={{ once: true }}
							whileHover={{ y: -5 }}
							className="group bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 hover:shadow-2xl transition-all duration-300"
						>
							<motion.div
								className="mb-6 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 p-4 rounded-xl"
								whileHover={{ scale: 1.05 }}
								transition={{ type: "spring", stiffness: 300 }}
							>
								{feature.icon}
							</motion.div>
							<h3 className="text-xl font-semibold mb-3 text-gray-900 dark:text-white">
								{feature.title}
							</h3>
							<p className="text-gray-600 dark:text-gray-300 mb-6">
								{feature.description}
							</p>
							<div className="space-y-4">
								<div className="flex items-center justify-between">
									<div className="flex items-center -space-x-2">
										{feature.activeUsers.map((user, i) => (
											<Image
												key={i}
												src={getAvatarUrl(`${feature.title}-${user}`)}
												alt={user}
												width={28}
												height={28}
												className="rounded-full border-2 border-white dark:border-gray-800"
											/>
										))}
									</div>
									<span className="text-sm text-gray-500 dark:text-gray-400 flex items-center">
										<Zap className="w-4 h-4 mr-1" />
										{feature.activity}
									</span>
								</div>
								<motion.button
									whileHover={{ x: 5 }}
									className="w-full flex items-center justify-between px-4 py-2 rounded-lg bg-gray-50 dark:bg-gray-700/50 text-gray-600 dark:text-gray-300 text-sm group-hover:bg-purple-50 dark:group-hover:bg-purple-900/20 group-hover:text-purple-600 dark:group-hover:text-purple-300 transition-colors"
								>
									Learn more
									<ArrowRight className="w-4 h-4" />
								</motion.button>
							</div>
						</motion.div>
					))}
				</div>

				<div className="mt-20 text-center">
					<motion.div
						initial={{ opacity: 0, y: 20 }}
						whileInView={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.5 }}
						viewport={{ once: true }}
						className="mb-16"
					>
						<span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-300 mb-4">
							<LayoutGrid className="w-4 h-4 mr-2" />
							Walls System
						</span>
						<h2 className="text-3xl md:text-4xl font-bold mb-6">
							Infinite Space for Your Ideas
						</h2>
						<p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-4">
							Create and customize multiple walls to organize your widgets.
							Share walls with team members or keep them private for personal
							use.
						</p>
						<div className="flex items-center justify-center gap-4 text-sm text-gray-500 dark:text-gray-400">
							<span className="flex items-center">
								<Users className="w-4 h-4 mr-1" />
								Collaborative Walls
							</span>
							<span className="flex items-center">
								<LayoutGrid className="w-4 h-4 mr-1" />
								Unlimited Space
							</span>
							<span className="flex items-center">
								<ArrowRight className="w-4 h-4 mr-1" />
								Private Walls
							</span>
						</div>
					</motion.div>
					{renderWallArea()}
				</div>
			</div>
		</section>
	);
};

export default DashboardShowcase;

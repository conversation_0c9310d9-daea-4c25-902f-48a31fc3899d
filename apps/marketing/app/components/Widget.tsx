import React, { Suspense } from 'react'
import { WidgetConfig, loadWidget } from '../widgets/WidgetRegistry'

interface WidgetProps {
  config: WidgetConfig
}

const Widget: React.FC<WidgetProps> = ({ config }) => {
  const WidgetComponent = React.lazy(() => loadWidget(config.type))

  return (
    <Suspense fallback={<div>Loading...</div>}>
      <WidgetComponent config={config.config} />
    </Suspense>
  )
}

export default Widget


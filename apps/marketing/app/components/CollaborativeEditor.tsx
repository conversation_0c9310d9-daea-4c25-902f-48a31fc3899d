'use client'

import React, { useEffect, useState } from 'react'
import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Collaboration from '@tiptap/extension-collaboration'
import CollaborationCursor from '@tiptap/extension-collaboration-cursor'
import * as Y from 'yjs'
import { WebrtcProvider } from 'y-webrtc'
import { IndexeddbPersistence } from 'y-indexeddb'

const colors = ['#958DF1', '#F98181', '#FBBC88', '#FAF594', '#70CFF8', '#94FADB', '#B9F18D']
const getRandomColor = () => colors[Math.floor(Math.random() * colors.length)]
const getRandomName = () => `User ${Math.floor(Math.random() * 1000)}`

const CollaborativeEditor: React.FC = () => {
  const [status, setStatus] = useState('connecting')
  const [provider, setProvider] = useState<WebrtcProvider | null>(null)

  useEffect(() => {
    const ydoc = new Y.Doc()
    const persistence = new IndexeddbPersistence('buds-collab-editor', ydoc)
    const webrtcProvider = new WebrtcProvider('buds-collab-editor', ydoc, { signaling: ['wss://signaling.yjs.dev'] })

    persistence.once('synced', () => {
      setStatus('connected')
    })

    setProvider(webrtcProvider)

    return () => {
      webrtcProvider.destroy()
      persistence.destroy()
    }
  }, [])

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        history: false,
      }),
      Collaboration.configure({
        document: provider?.doc,
      }),
      CollaborationCursor.configure({
        provider: provider,
        user: { name: getRandomName(), color: getRandomColor() },
      }),
    ],
    content: '<p>Start collaborating here...</p>',
  })

  return (
    <div className="border rounded-lg p-4 bg-white shadow-lg">
      <div className="mb-2">
        Status: <span className={status === 'connected' ? 'text-green-500' : 'text-yellow-500'}>{status}</span>
      </div>
      <EditorContent editor={editor} />
    </div>
  )
}

export default CollaborativeEditor


import React from 'react'
import { WidgetConfig } from './WidgetRegistry'

interface TaskTrackerProps {
  config: WidgetConfig['config']
}

const TaskTracker: React.FC<TaskTrackerProps> = ({ config }) => {
  return (
    <div className="p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
      <h3 className="text-lg font-semibold mb-2">Task Tracker</h3>
      <ul className="space-y-2">
        {config.tasks?.map((task: string, index: number) => (
          <li key={index} className="flex items-center">
            <input type="checkbox" className="mr-2" />
            <span>{task}</span>
          </li>
        ))}
      </ul>
    </div>
  )
}

export default TaskTracker


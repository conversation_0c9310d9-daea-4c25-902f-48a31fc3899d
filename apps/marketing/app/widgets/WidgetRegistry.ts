import { lazy } from 'react'

export interface WidgetConfig {
  id: string
  type: string
  config: Record<string, any>
  position: {
    x: number
    y: number
    width: number
    height: number
  }
}

export const WidgetRegistry = {
  TaskTracker: lazy(() => import('./TaskTracker')),
  Notes: lazy(() => import('./Notes')),
  Graph: lazy(() => import('./Graph')),
  CollaborativeEditor: lazy(() => import('../components/CollaborativeEditor')),
}

export const loadWidget = async (type: string) => WidgetRegistry[type as keyof typeof WidgetRegistry]


"use client";

import React, { useState, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
	<PERSON>,
	CardContent,
	Card<PERSON><PERSON>er,
	CardTitle,
} from "@/app/components/ui/card";
import { SportsWidget } from "./components/SportsWidget";
import { AirlineWidget } from "./components/AirlineWidget";
import { WeatherWidget } from "./components/WeatherWidget";
import { Button } from "@/app/components/ui/button";
import { Settings } from "lucide-react";
import { ClockWidget } from "./components/ClockWidget";
import { CalendarWidget } from "./components/CalendarWidget";
import { ChartWidget } from "./components/ChartWidget";
import { WorldClockWidget } from "./components/WorldClockWidget";
import { NotesWidget } from "./components/NotesWidget";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@/app/components/ui/tooltip";

interface Widget {
	id: string;
	component: React.ReactNode;
	position: { x: number; y: number };
}

function createWidget(id: string, position: { x: number; y: number }): Widget {
	switch (id) {
		case "clock":
			return { id, component: <ClockWidget />, position };
		case "calendar":
			return { id, component: <CalendarWidget />, position };
		case "chart":
			return { id, component: <ChartWidget />, position };
		case "worldclock":
			return { id, component: <WorldClockWidget />, position };
		case "notes":
			return { id, component: <NotesWidget />, position };
		case "sports":
			return { id, component: <SportsWidget />, position };
		case "airline":
			return { id, component: <AirlineWidget />, position };
		case "weather":
			return { id, component: <WeatherWidget />, position };
		default:
			throw new Error(`Unknown widget type: ${id}`);
	}
}

export default function Dashboard() {
	const [widgets, setWidgets] = useState<Widget[]>([
		{ id: "sports", component: <SportsWidget />, position: { x: 0, y: 0 } },
		{ id: "airline", component: <AirlineWidget />, position: { x: 300, y: 0 } },
		{
			id: "weather",
			component: <WeatherWidget />,
			position: { x: 100, y: 300 },
		},
	]);

	const handleDrag = (id: string, newPosition: { x: number; y: number }) => {
		setWidgets(
			widgets.map((widget) =>
				widget.id === id ? { ...widget, position: newPosition } : widget
			)
		);
	};

	return (
		<div className="relative min-h-screen p-6">
			<AnimatePresence>
				{widgets.map((widget) => (
					<motion.div
						key={widget.id}
						drag
						dragMomentum={false}
						initial={{ opacity: 0, scale: 0.8 }}
						animate={{ opacity: 1, scale: 1 }}
						exit={{ opacity: 0, scale: 0.8 }}
						transition={{ type: "spring", stiffness: 500, damping: 30 }}
						style={{ position: "absolute", zIndex: 10, ...widget.position }}
						onDragEnd={(_, info) => handleDrag(widget.id, info.point)}
						className="group"
					>
						<Card className="w-64 shadow-lg bg-white dark:bg-gray-900 border dark:border-gray-800">
							<CardHeader className="bg-gray-50 dark:bg-gray-800 rounded-t-lg border-b dark:border-gray-800 flex flex-row items-center justify-between space-y-0 pb-2">
								<CardTitle className="text-base font-semibold text-gray-900 dark:text-gray-100">
									{widget.id.charAt(0).toUpperCase() + widget.id.slice(1)}
								</CardTitle>
								<TooltipProvider>
									<Tooltip>
										<TooltipTrigger asChild>
											<Button
												variant="ghost"
												size="icon"
												className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-gray-100 dark:hover:bg-gray-800"
											>
												<Settings className="h-4 w-4" />
											</Button>
										</TooltipTrigger>
										<TooltipContent side="top" align="center" sideOffset={4}>
											<span className="font-medium">Widget Settings</span>
										</TooltipContent>
									</Tooltip>
								</TooltipProvider>
							</CardHeader>
							<CardContent className="p-4 bg-white dark:bg-gray-900">
								{widget.component}
							</CardContent>
						</Card>
					</motion.div>
				))}
			</AnimatePresence>
		</div>
	);
}

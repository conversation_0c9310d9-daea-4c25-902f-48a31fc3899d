export default function DotBackground({ children }: { children: React.ReactNode }) {
  return (
    <div className="relative min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute left-0 top-0 -z-10 h-full w-full">
          <svg
            className="h-full w-full"
            xmlns="http://www.w3.org/2000/svg"
            width="100%"
            height="100%"
          >
            <defs>
              <pattern
                id="dotPattern"
                width="16"
                height="16"
                patternUnits="userSpaceOnUse"
              >
                <circle
                  cx="2"
                  cy="2"
                  r="1"
                  fill="currentColor"
                  className="text-gray-200 dark:text-gray-800"
                />
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#dotPattern)" />
          </svg>
        </div>
      </div>
      <div className="relative z-10">{children}</div>
    </div>
  )
}


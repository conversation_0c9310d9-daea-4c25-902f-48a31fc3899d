import { useState } from "react";
import { <PERSON>, <PERSON>, <PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/app/components/ui/button";
import {
	Toolt<PERSON>,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@/app/components/ui/tooltip";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/app/components/ui/select";
import { Switch } from "@/app/components/ui/switch";
import { Label } from "@/app/components/ui/label";

const colorOptions = [
	{ name: "None", class: "bg-none", gradientClass: "bg-none" },
	{
		name: "Slate",
		class: "bg-slate-500",
		gradientClass: "bg-gradient-to-r from-slate-400 to-slate-600",
	},
	{
		name: "Red",
		class: "bg-red-500",
		gradientClass: "bg-gradient-to-r from-red-400 to-red-600",
	},
	{
		name: "<PERSON>",
		class: "bg-green-500",
		gradientClass: "bg-gradient-to-r from-green-400 to-green-600",
	},
	{
		name: "Blue",
		class: "bg-blue-500",
		gradientClass: "bg-gradient-to-r from-blue-400 to-blue-600",
	},
	{
		name: "Purple",
		class: "bg-purple-500",
		gradientClass: "bg-gradient-to-r from-purple-400 to-purple-600",
	},
];

const pillStyles = [
	{ name: "Basic", value: "basic" },
	{ name: "Gradient", value: "gradient" },
] as const;

type PillStyle = (typeof pillStyles)[number]["value"];

interface PillColorSettingsProps {
	pillStyle: PillStyle;
	setPillStyle: (style: PillStyle) => void;
	pillColor: string;
	setPillColor: (color: string) => void;
	isOutlined: boolean;
	setIsOutlined: (outlined: boolean) => void;
	isDarkMode: boolean;
	toggleDarkMode: () => void;
}

export function PillColorSettings({
	pillStyle,
	setPillStyle,
	pillColor,
	setPillColor,
	isOutlined,
	setIsOutlined,
	isDarkMode,
	toggleDarkMode,
}: PillColorSettingsProps) {
	return (
		<div className="space-y-3">
			<div>
				<h4 className="text-sm font-medium mb-2">Pill Style</h4>
				<Select
					value={pillStyle}
					onValueChange={(value: PillStyle) => setPillStyle(value)}
				>
					<SelectTrigger className="w-full">
						<SelectValue placeholder="Select a style" />
					</SelectTrigger>
					<SelectContent>
						{pillStyles.map((style) => (
							<SelectItem key={style.value} value={style.value}>
								{style.name}
							</SelectItem>
						))}
					</SelectContent>
				</Select>
			</div>
			<div className="flex items-center space-x-2">
				<Switch
					checked={isOutlined}
					onCheckedChange={setIsOutlined}
					id="outlined-mode"
				/>
				<Label htmlFor="outlined-mode">Outlined</Label>
			</div>
			<div>
				<h4 className="text-sm font-medium mb-2">Pill Color</h4>
				<div className="flex flex-wrap gap-2">
					{colorOptions.map((color) => (
						<TooltipProvider key={color.name}>
							<Tooltip>
								<TooltipTrigger asChild>
									<Button
										variant="ghost"
										size="sm"
										className={`w-8 h-8 rounded-full p-0 ${
											pillStyle === "gradient"
												? color.gradientClass
												: color.class
										} ${
											pillColor === color.class ? "ring-2 ring-primary" : ""
										}`}
										onClick={() => setPillColor(color.class)}
									>
										{color.name === "None" && (
											<X className="w-4 h-4 text-red-500" />
										)}
									</Button>
								</TooltipTrigger>
								<TooltipContent>
									<span>{color.name}</span>
								</TooltipContent>
							</Tooltip>
						</TooltipProvider>
					))}
				</div>
			</div>
		</div>
	);
}

'use client'

import { useState, useEffect } from 'react'
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd'
import { motion } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import SwipeableCard from './SwipeableCard'

const initialItems = [
  { id: 'item1', content: 'Task List', color: 'bg-blue-100 dark:bg-blue-900' },
  { id: 'item2', content: 'Calendar', color: 'bg-green-100 dark:bg-green-900' },
  { id: 'item3', content: 'Notes', color: 'bg-yellow-100 dark:bg-yellow-900' },
  { id: 'item4', content: 'Files', color: 'bg-purple-100 dark:bg-purple-900' },
]

export default function DraggableGrid() {
  const [items, setItems] = useState(initialItems)
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  const onDragEnd = (result) => {
    if (!result.destination) {
      return
    }

    const newItems = Array.from(items)
    const [reorderedItem] = newItems.splice(result.source.index, 1)
    newItems.splice(result.destination.index, 0, reorderedItem)

    setItems(newItems)
  }

  if (isMobile) {
    return (
      <div className="space-y-4">
        {items.map((item) => (
          <SwipeableCard key={item.id} id={item.id} content={item.content} color={item.color} />
        ))}
      </div>
    )
  }

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <Droppable droppableId="grid" direction="horizontal">
        {(provided) => (
          <div
            {...provided.droppableProps}
            ref={provided.innerRef}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
          >
            {items.map((item, index) => (
              <Draggable key={item.id} draggableId={item.id} index={index}>
                {(provided, snapshot) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                    {...provided.dragHandleProps}
                    style={{
                      ...provided.draggableProps.style,
                      transform: snapshot.isDragging ? provided.draggableProps.style.transform : 'none',
                    }}
                  >
                    <motion.div
                      whileHover={{ scale: 1.03 }}
                      whileTap={{ scale: 0.97 }}
                    >
                      <Card className={`${item.color} shadow-lg`}>
                        <CardHeader>
                          <CardTitle>{item.content}</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <p>Content for {item.content}</p>
                        </CardContent>
                      </Card>
                    </motion.div>
                  </div>
                )}
              </Draggable>
            ))}
            {provided.placeholder}
          </div>
        )}
      </Droppable>
    </DragDropContext>
  )
}


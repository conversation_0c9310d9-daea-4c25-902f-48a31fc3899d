'use client'

import React, { useRef, useEffect, useState } from 'react'
import { motion, useMotionValue, useTransform, useSpring } from 'framer-motion'

const GRID_SIZE = 20
const MIN_ZOOM = 0.5
const MAX_ZOOM = 2

export const InfiniteGrid: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null)
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 })

  const x = useMotionValue(0)
  const y = useMotionValue(0)
  const zoom = useMotionValue(1)

  const gridSize = useTransform(zoom, (value) => GRID_SIZE * value)

  const springConfig = { stiffness: 1000, damping: 50, mass: 1 }
  const scaleSpring = useSpring(zoom, springConfig)

  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        setDimensions({
          width: containerRef.current.offsetWidth,
          height: containerRef.current.offsetHeight,
        })
      }
    }

    updateDimensions()
    window.addEventListener('resize', updateDimensions)
    return () => window.removeEventListener('resize', updateDimensions)
  }, [])

  const handleWheel = (event: React.WheelEvent) => {
    if (event.ctrlKey) {
      event.preventDefault()
      const newZoom = zoom.get() - event.deltaY * 0.001
      zoom.set(Math.min(Math.max(newZoom, MIN_ZOOM), MAX_ZOOM))
    } else {
      x.set(x.get() - event.deltaX)
      y.set(y.get() - event.deltaY)
    }
  }

  return (
    <div
      ref={containerRef}
      className="fixed inset-0 overflow-hidden"
      onWheel={handleWheel}
    >
      <motion.div
        style={{
          width: dimensions.width,
          height: dimensions.height,
          x,
          y,
          scale: scaleSpring,
        }}
        className="absolute inset-0"
      >
        <svg width="100%" height="100%">
          <defs>
            <pattern
              id="grid"
              width={gridSize}
              height={gridSize}
              patternUnits="userSpaceOnUse"
            >
              <path
                d={`M ${gridSize} 0 L 0 0 0 ${gridSize}`}
                fill="none"
                stroke="var(--dot-color)"
                strokeWidth="0.5"
              />
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />
        </svg>
      </motion.div>
    </div>
  )
}


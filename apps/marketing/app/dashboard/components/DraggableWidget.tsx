'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, useMotionValue, useTransform } from 'framer-motion'
import { GripHorizontal } from 'lucide-react'

interface DraggableWidgetProps {
  children: React.ReactNode
  title: string
  initialPosition?: { x: number; y: number }
}

const DraggableWidget: React.FC<DraggableWidgetProps> = ({ children, title, initialPosition }) => {
  const [isDragging, setIsDragging] = useState(false)
  const x = useMotionValue(initialPosition?.x || 0)
  const y = useMotionValue(initialPosition?.y || 0)

  const onDragStart = () => setIsDragging(true)
  const onDragEnd = () => setIsDragging(false)

  return (
    <motion.div
      drag
      dragMomentum={false}
      dragElastic={0}
      onDragStart={onDragStart}
      onDragEnd={onDragEnd}
      style={{ 
        x, 
        y, 
        zIndex: useTransform(y, (latest) => Math.round(latest)),
        position: 'absolute',
      }}
      className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 w-[300px] overflow-hidden"
    >
      <div className="p-3 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600 flex items-center justify-between cursor-move">
        <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-200">{title}</h3>
        <GripHorizontal className="w-4 h-4 text-gray-400" />
      </div>
      <div className="p-4">
        {children}
      </div>
    </motion.div>
  )
}

export default DraggableWidget


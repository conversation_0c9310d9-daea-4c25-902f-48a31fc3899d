import React, { useState, useEffect } from "react";

export function ClockWidget() {
	const [time, setTime] = useState(new Date());

	useEffect(() => {
		const timer = setInterval(() => setTime(new Date()), 1000);
		return () => clearInterval(timer);
	}, []);

	return (
		<div className="flex flex-col items-center justify-center space-y-2">
			<div className="text-3xl font-bold text-primary">
				{time.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
			</div>
			<div className="text-sm text-muted-foreground">
				{time.toLocaleDateString([], {
					weekday: "long",
					month: "long",
					day: "numeric",
				})}
			</div>
		</div>
	);
}

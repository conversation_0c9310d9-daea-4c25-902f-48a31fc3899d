'use client'

import React, { useEffect, useState } from 'react'
import { useParams } from 'next/navigation'
import * as Y from 'yjs'
import { WebsocketProvider } from 'y-websocket'
import { Responsive, WidthProvider } from 'react-grid-layout'
import Widget from '../../components/Widget'
import { WidgetConfig } from '../../widgets/WidgetRegistry'

const ResponsiveGridLayout = WidthProvider(Responsive)

export default function CollaborativeRoom() {
  const { roomId } = useParams()
  const [widgets, setWidgets] = useState<WidgetConfig[]>([])

  useEffect(() => {
    const doc = new Y.Doc()
    const wsProvider = new WebsocketProvider('ws://localhost:1234', roomId as string, doc)
    const roomState = doc.getMap('widgets')

    const updateWidgets = () => {
      const widgetsArray: WidgetConfig[] = []
      roomState.forEach((value, key) => {
        widgetsArray.push({ id: key, ...value })
      })
      setWidgets(widgetsArray)
    }

    roomState.observe(updateWidgets)
    updateWidgets()

    return () => {
      wsProvider.disconnect()
    }
  }, [roomId])

  const onLayoutChange = (layout: any) => {
    // Update Yjs shared state
  }

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Collaborative Room: {roomId}</h1>
      <ResponsiveGridLayout
        className="layout"
        layouts={{ lg: widgets.map((w) => ({ ...w.position, i: w.id })) }}
        breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 }}
        cols={{ lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 }}
        rowHeight={100}
        onLayoutChange={onLayoutChange}
      >
        {widgets.map((widget) => (
          <div key={widget.id}>
            <Widget config={widget} />
          </div>
        ))}
      </ResponsiveGridLayout>
    </div>
  )
}


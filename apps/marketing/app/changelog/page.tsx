import { Metadata } from "next";
import Header from "../components/Header";

export const metadata: Metadata = {
	title: "Changelog | BudsCollab",
	description: "Latest updates and improvements to BudsCollab",
};

type ChangelogEntry = {
	date: string;
	version: string;
	changes: {
		type: "feature" | "improvement" | "fix";
		description: string;
	}[];
};

// This would typically come from a database or CMS
const CHANGELOG_DATA: ChangelogEntry[] = [
	{
		date: "March 15, 2024",
		version: "1.2.0",
		changes: [
			{
				type: "feature",
				description: "Added real-time collaboration features",
			},
			{
				type: "improvement",
				description: "Enhanced dashboard performance",
			},
			{
				type: "fix",
				description: "Fixed user authentication issues",
			},
		],
	},
	{
		date: "March 1, 2024",
		version: "1.1.0",
		changes: [
			{
				type: "feature",
				description: "Introduced dark mode support",
			},
			{
				type: "improvement",
				description: "Updated UI components for better accessibility",
			},
		],
	},
];

function ChangeTypeIcon({
	type,
}: {
	type: ChangelogEntry["changes"][0]["type"];
}) {
	const iconClasses = "w-5 h-5";

	switch (type) {
		case "feature":
			return (
				<svg
					className={`${iconClasses} text-emerald-500`}
					fill="none"
					viewBox="0 0 24 24"
					stroke="currentColor"
				>
					<path
						strokeLinecap="round"
						strokeLinejoin="round"
						strokeWidth={2}
						d="M12 6v6m0 0v6m0-6h6m-6 0H6"
					/>
				</svg>
			);
		case "improvement":
			return (
				<svg
					className={`${iconClasses} text-black dark:text-white`}
					fill="none"
					viewBox="0 0 24 24"
					stroke="currentColor"
				>
					<path
						strokeLinecap="round"
						strokeLinejoin="round"
						strokeWidth={2}
						d="M13 10V3L4 14h7v7l9-11h-7z"
					/>
				</svg>
			);
		case "fix":
			return (
				<svg
					className={`${iconClasses} text-red-500`}
					fill="none"
					viewBox="0 0 24 24"
					stroke="currentColor"
				>
					<path
						strokeLinecap="round"
						strokeLinejoin="round"
						strokeWidth={2}
						d="M12 14l9-5-9-5-9 5 9 5z"
					/>
				</svg>
			);
	}
}

export default function ChangelogPage() {
	return (
		<>
			<Header />
			<main className="min-h-screen bg-background pt-24">
				<div className="max-w-4xl mx-auto px-4 py-16 sm:px-6 lg:px-8">
					<div className="text-center mb-16">
						<h1 className="text-4xl font-bold text-foreground mb-4">
							Changelog
						</h1>
						<p className="text-lg text-foreground/60">
							Keep track of updates and improvements to BudsCollab
						</p>
					</div>

					<div className="relative">
						{/* Version filter pills */}
						<div className="flex gap-2 mb-12 overflow-x-auto pb-2 -mx-4 px-4 sm:mx-0 sm:px-0">
							<button className="inline-flex items-center px-4 py-2 rounded-full bg-black text-white dark:bg-white dark:text-black text-sm font-medium">
								All Versions
							</button>
							<button className="inline-flex items-center px-4 py-2 rounded-full bg-black/10 text-black dark:bg-white/10 dark:text-white text-sm font-medium hover:bg-black/20 dark:hover:bg-white/20 transition-colors">
								v1.2.0
							</button>
							<button className="inline-flex items-center px-4 py-2 rounded-full bg-black/10 text-black dark:bg-white/10 dark:text-white text-sm font-medium hover:bg-black/20 dark:hover:bg-white/20 transition-colors">
								v1.1.0
							</button>
						</div>

						<div className="space-y-16">
							{CHANGELOG_DATA.map((entry, index) => (
								<div key={index} className="relative">
									{index !== CHANGELOG_DATA.length - 1 && (
										<div className="absolute left-8 top-20 bottom-0 w-px bg-black/20 dark:bg-white/20" />
									)}

									<div className="relative">
										<div className="flex items-center mb-6">
											<div className="flex-shrink-0 h-16 w-16 rounded-full bg-black/5 dark:bg-white/5 flex items-center justify-center border-2 border-black/20 dark:border-white/20">
												<span className="text-black dark:text-white font-semibold">
													{entry.version}
												</span>
											</div>
											<div className="ml-6">
												<h2 className="text-2xl font-semibold text-foreground">
													{entry.date}
												</h2>
											</div>
										</div>

										<div className="ml-16 space-y-6">
											{entry.changes.map((change, changeIndex) => (
												<div
													key={changeIndex}
													className="flex items-start p-4 rounded-lg hover:bg-black/5 dark:hover:bg-white/5 transition-colors"
												>
													<div className="flex-shrink-0 mt-1">
														<ChangeTypeIcon type={change.type} />
													</div>
													<div className="ml-4">
														<span className="text-sm font-medium capitalize text-foreground/60">
															{change.type}
														</span>
														<p className="mt-1 text-foreground">
															{change.description}
														</p>
													</div>
												</div>
											))}
										</div>
									</div>
								</div>
							))}
						</div>
					</div>
				</div>
			</main>
		</>
	);
}

"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import Header from "./components/Header";
import Hero from "./components/Hero";
import DashboardShowcase from "./components/DashboardShowcase";
import Features from "./components/Features";
import Footer from "./components/Footer";

export default function Home() {
	const router = useRouter();

	useEffect(() => {
		const handleKeyPress = (event: KeyboardEvent) => {
			if (
				event.key.toLowerCase() === "l" &&
				!event.metaKey &&
				!event.ctrlKey &&
				!event.altKey
			) {
				router.push("/dashboard");
			}
		};

		window.addEventListener("keydown", handleKeyPress);

		return () => {
			window.removeEventListener("keydown", handleKeyPress);
		};
	}, [router]);

	return (
		<main>
			<Header isHomePage={true} />
			<Hero />
			<DashboardShowcase />
			<Features />
			<Footer />
		</main>
	);
}

"use client";

import { useState, useEffect } from "react";
import GridSystem from "./components/GridSystem";
import { Widget } from "./types/grid";

export default function DashboardPage() {
	const [widgets, setWidgets] = useState<Widget[]>([]);

	// Load widgets from localStorage on mount
	useEffect(() => {
		const savedWidgets = localStorage.getItem("dashboard-widgets");
		if (savedWidgets) {
			try {
				setWidgets(JSON.parse(savedWidgets));
			} catch (error) {
				console.error("Failed to load widgets:", error);
			}
		} else {
			// Default widgets for first-time users - properly spaced with new 7th column
			const defaultWidgets: Widget[] = [
				{
					id: "default-editor",
					type: "collaborative-editor",
					title: "Collaborative Editor",
					position: { x: 0, y: 0, w: 4, h: 3 },
				},
				{
					id: "default-tasks",
					type: "task-list",
					title: "My Tasks",
					position: { x: 4, y: 0, w: 2, h: 3 },
				},
				{
					id: "default-clock",
					type: "clock",
					title: "Clock",
					position: { x: 6, y: 0, w: 1, h: 2 },
				},
				{
					id: "default-notes",
					type: "notes",
					title: "Quick Notes",
					position: { x: 0, y: 3, w: 3, h: 2 },
				},
			];
			setWidgets(defaultWidgets);
		}
	}, []);

w	useEffect(() => {
		if (widgets.length > 0) {
			localStorage.setItem("dashboard-widgets", JSON.stringify(widgets));
		}
	}, [widgets]);

	return (
		<GridSystem
			widgets={widgets}
			onWidgetsChange={setWidgets}
			gridConfig={{
				cols: 7,
				rows: 5,
				cellSize: 120,
				gap: 16,
			}}
		/>
	);
}

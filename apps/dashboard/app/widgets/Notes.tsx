import React from 'react'
import { WidgetConfig } from './WidgetRegistry'

interface NotesProps {
  config: WidgetConfig['config']
}

const Notes: React.FC<NotesProps> = ({ config }) => {
  return (
    <div className="p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
      <h3 className="text-lg font-semibold mb-2">Notes</h3>
      <textarea
        className="w-full h-32 p-2 border rounded"
        defaultValue={config.content || ''}
        placeholder="Enter your notes here..."
      />
    </div>
  )
}

export default Notes


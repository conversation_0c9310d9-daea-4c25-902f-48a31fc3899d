"use client";

import { useState, useEffect } from "react";
import { WidgetComponentProps } from "../types/grid";
import { Clock, Globe } from "lucide-react";

export default function ClockWidget({ widget }: WidgetComponentProps) {
  const [time, setTime] = useState(new Date());
  const [timezone, setTimezone] = useState("local");

  useEffect(() => {
    const timer = setInterval(() => {
      setTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatTime = (date: Date) => {
    if (timezone === "local") {
      return date.toLocaleTimeString([], { 
        hour: '2-digit', 
        minute: '2-digit',
        second: '2-digit'
      });
    }
    // Add timezone support later
    return date.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString([], {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });
  };

  const isCompact = widget.position.w <= 2 || widget.position.h <= 1;

  return (
    <div className="h-full flex flex-col items-center justify-center gap-2">
      {/* Time Display */}
      <div className="text-center">
        <div className={`font-mono font-bold text-foreground ${
          isCompact ? "text-lg" : "text-2xl"
        }`}>
          {formatTime(time)}
        </div>
        {!isCompact && (
          <div className="text-sm text-muted-foreground">
            {formatDate(time)}
          </div>
        )}
      </div>

      {/* Timezone Indicator */}
      {!isCompact && (
        <div className="flex items-center gap-1 text-xs text-muted-foreground">
          <Globe className="h-3 w-3" />
          <span>Local Time</span>
        </div>
      )}

      {/* Clock Icon for very compact view */}
      {isCompact && widget.position.w === 1 && (
        <Clock className="h-4 w-4 text-muted-foreground" />
      )}
    </div>
  );
}

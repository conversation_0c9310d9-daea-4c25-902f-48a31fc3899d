"use client";

import React, { useState, useEffect } from "react";
import { Cloud, Sun, CloudRain, CloudSnow, Wind, Thermometer, Droplets } from "lucide-react";
import { WidgetComponentProps } from "../types/grid";

interface WeatherData {
  temperature: number;
  condition: string;
  humidity: number;
  windSpeed: number;
  location: string;
  forecast: {
    day: string;
    high: number;
    low: number;
    condition: string;
  }[];
}

const WeatherWidget: React.FC<WidgetComponentProps> = ({ widget }) => {
  const [weather, setWeather] = useState<WeatherData | null>(null);
  const [loading, setLoading] = useState(true);

  // Mock weather data - in a real app, this would fetch from a weather API
  useEffect(() => {
    const mockWeather: WeatherData = {
      temperature: 72,
      condition: "partly-cloudy",
      humidity: 65,
      windSpeed: 8,
      location: "San Francisco, CA",
      forecast: [
        { day: "Today", high: 75, low: 62, condition: "sunny" },
        { day: "Tomorrow", high: 73, low: 60, condition: "cloudy" },
        { day: "Wed", high: 68, low: 58, condition: "rainy" },
      ]
    };

    setTimeout(() => {
      setWeather(mockWeather);
      setLoading(false);
    }, 1000);
  }, []);

  const getWeatherIcon = (condition: string, size: "sm" | "lg" = "sm") => {
    const iconSize = size === "lg" ? "h-8 w-8" : "h-4 w-4";
    
    switch (condition) {
      case "sunny":
        return <Sun className={`${iconSize} text-yellow-500`} />;
      case "cloudy":
        return <Cloud className={`${iconSize} text-gray-500`} />;
      case "partly-cloudy":
        return <Cloud className={`${iconSize} text-gray-400`} />;
      case "rainy":
        return <CloudRain className={`${iconSize} text-blue-500`} />;
      case "snowy":
        return <CloudSnow className={`${iconSize} text-blue-200`} />;
      default:
        return <Sun className={`${iconSize} text-yellow-500`} />;
    }
  };

  if (loading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-sm text-muted-foreground">Loading weather...</div>
      </div>
    );
  }

  if (!weather) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-sm text-muted-foreground">Weather unavailable</div>
      </div>
    );
  }

  const isCompact = widget.position.w === 1 && widget.position.h === 1;
  const isWide = widget.position.w >= 2;

  if (isCompact) {
    // 1x1 - Minimal view
    return (
      <div className="h-full flex flex-col items-center justify-center">
        {getWeatherIcon(weather.condition, "lg")}
        <div className="text-lg font-bold mt-1">{weather.temperature}°</div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Current weather */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          {getWeatherIcon(weather.condition, "lg")}
          <div>
            <div className="text-2xl font-bold">{weather.temperature}°F</div>
            <div className="text-xs text-muted-foreground capitalize">
              {weather.condition.replace('-', ' ')}
            </div>
          </div>
        </div>
      </div>

      {/* Location */}
      <div className="text-xs text-muted-foreground mb-3">{weather.location}</div>

      {/* Additional details for wider widgets */}
      {isWide && (
        <div className="flex items-center gap-4 mb-4 text-xs">
          <div className="flex items-center gap-1">
            <Droplets className="h-3 w-3" />
            <span>{weather.humidity}%</span>
          </div>
          <div className="flex items-center gap-1">
            <Wind className="h-3 w-3" />
            <span>{weather.windSpeed} mph</span>
          </div>
        </div>
      )}

      {/* Forecast for taller widgets */}
      {widget.position.h >= 2 && (
        <div className="flex-1">
          <div className="text-xs font-medium text-muted-foreground mb-2">3-Day Forecast</div>
          <div className="space-y-2">
            {weather.forecast.map((day, index) => (
              <div key={index} className="flex items-center justify-between text-xs">
                <span className="w-12">{day.day}</span>
                <div className="flex items-center gap-2">
                  {getWeatherIcon(day.condition)}
                  <span className="w-8 text-right">{day.high}°</span>
                  <span className="w-8 text-right text-muted-foreground">{day.low}°</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default WeatherWidget;

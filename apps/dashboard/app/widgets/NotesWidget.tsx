"use client";

import { useState, useEffect } from "react";
import { WidgetComponentProps } from "../types/grid";
import { Save, Edit3 } from "lucide-react";
import { Button } from "../components/ui/button";

export default function NotesWidget({ widget, onUpdate }: WidgetComponentProps) {
  const [notes, setNotes] = useState("");
  const [isEditing, setIsEditing] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  // Load notes from widget config
  useEffect(() => {
    if (widget.config?.notes) {
      setNotes(widget.config.notes);
    }
  }, [widget.config]);

  // Auto-save notes
  useEffect(() => {
    const timer = setTimeout(() => {
      if (notes !== (widget.config?.notes || "")) {
        saveNotes();
      }
    }, 1000);

    return () => clearTimeout(timer);
  }, [notes, widget.config?.notes]);

  const saveNotes = () => {
    onUpdate?.({
      ...widget,
      config: { ...widget.config, notes },
    });
    setLastSaved(new Date());
  };

  const isCompact = widget.position.w <= 2 || widget.position.h <= 2;

  return (
    <div className="h-full flex flex-col gap-3 overflow-hidden">
      {/* Header */}
      <div className="flex-shrink-0 flex items-center justify-between">
        <div>
          <h3 className="text-sm font-semibold text-foreground">
            {isCompact ? "Notes" : "Quick Notes"}
          </h3>
          {!isCompact && lastSaved && (
            <p className="text-xs text-muted-foreground">
              Saved {lastSaved.toLocaleTimeString()}
            </p>
          )}
        </div>
        <div className="flex items-center gap-1">
          <Button
            size="sm"
            variant="ghost"
            onClick={() => setIsEditing(!isEditing)}
            className="h-6 w-6 p-0"
          >
            <Edit3 className="h-3 w-3" />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={saveNotes}
            className="h-6 w-6 p-0"
          >
            <Save className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Notes Content */}
      <div className="flex-1 overflow-hidden">
        {isEditing || notes === "" ? (
          <textarea
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            placeholder={isCompact ? "Write notes..." : "Write your notes here..."}
            className="w-full h-full p-2 text-xs bg-background border border-input rounded text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring resize-none"
            autoFocus={isEditing}
          />
        ) : (
          <div
            className="w-full h-full p-2 text-xs bg-muted rounded overflow-y-auto cursor-pointer hover:bg-muted/80 transition-colors"
            onClick={() => setIsEditing(true)}
          >
            {notes ? (
              <pre className="whitespace-pre-wrap text-foreground font-sans">
                {notes}
              </pre>
            ) : (
              <p className="text-muted-foreground italic">
                Click to add notes...
              </p>
            )}
          </div>
        )}
      </div>

      {/* Character Count */}
      {!isCompact && notes.length > 0 && (
        <div className="flex-shrink-0 text-xs text-muted-foreground text-right">
          {notes.length} characters
        </div>
      )}
    </div>
  );
}

"use client";

import React, { useState } from "react";
import { Globe, ExternalLink, RefreshCw, Settings } from "lucide-react";
import { Button } from "../components/ui/button";
import { Input } from "../components/ui/input";
import { WidgetComponentProps } from "../types/grid";

const WebViewWidget: React.FC<WidgetComponentProps> = ({ widget, onUpdate }) => {
  const [url, setUrl] = useState(widget.config?.url || "https://example.com");
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleUrlChange = (newUrl: string) => {
    setUrl(newUrl);
    if (onUpdate) {
      onUpdate({
        ...widget,
        config: { ...widget.config, url: newUrl }
      });
    }
  };

  const handleRefresh = () => {
    setLoading(true);
    // Simulate refresh delay
    setTimeout(() => setLoading(false), 1000);
  };

  const openInNewTab = () => {
    window.open(url, '_blank');
  };

  const isValidUrl = (urlString: string) => {
    try {
      new URL(urlString);
      return true;
    } catch {
      return false;
    }
  };

  const isCompact = widget.position.w === 2 && widget.position.h === 2;

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2 flex-1 min-w-0">
          <Globe className="h-4 w-4 text-primary flex-shrink-0" />
          {isEditing ? (
            <Input
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              onBlur={() => {
                setIsEditing(false);
                handleUrlChange(url);
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  setIsEditing(false);
                  handleUrlChange(url);
                }
              }}
              className="h-6 text-xs"
              placeholder="Enter URL..."
              autoFocus
            />
          ) : (
            <span 
              className="text-xs text-muted-foreground truncate cursor-pointer hover:text-foreground"
              onClick={() => setIsEditing(true)}
              title={url}
            >
              {url.replace(/^https?:\/\//, '')}
            </span>
          )}
        </div>
        
        <div className="flex gap-1 flex-shrink-0">
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={() => setIsEditing(!isEditing)}
          >
            <Settings className="h-3 w-3" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={handleRefresh}
            disabled={loading}
          >
            <RefreshCw className={`h-3 w-3 ${loading ? 'animate-spin' : ''}`} />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={openInNewTab}
          >
            <ExternalLink className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Web content area */}
      <div className="flex-1 border rounded bg-background overflow-hidden">
        {isValidUrl(url) ? (
          <iframe
            src={url}
            className="w-full h-full border-0"
            title="Web View"
            sandbox="allow-scripts allow-same-origin allow-forms allow-popups"
            loading="lazy"
          />
        ) : (
          <div className="h-full flex flex-col items-center justify-center text-muted-foreground">
            <Globe className="h-8 w-8 mb-2" />
            <div className="text-sm">Invalid URL</div>
            <div className="text-xs mt-1">Please enter a valid web address</div>
          </div>
        )}
      </div>

      {/* Footer info for larger widgets */}
      {!isCompact && (
        <div className="mt-2 text-xs text-muted-foreground">
          <div className="flex items-center justify-between">
            <span>Web View</span>
            <span className={`w-2 h-2 rounded-full ${loading ? 'bg-yellow-500' : 'bg-green-500'}`} />
          </div>
        </div>
      )}
    </div>
  );
};

export default WebViewWidget;

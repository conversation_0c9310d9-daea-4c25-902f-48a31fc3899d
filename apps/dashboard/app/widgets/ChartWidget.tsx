"use client";

import React, { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, TrendingUp } from "lucide-react";
import { Button } from "../components/ui/button";
import { WidgetComponentProps } from "../types/grid";

interface ChartData {
  labels: string[];
  data: number[];
  type: 'bar' | 'line' | 'pie';
}

const ChartWidget: React.FC<WidgetComponentProps> = ({ widget }) => {
  const [chartType, setChartType] = useState<'bar' | 'line' | 'pie'>('bar');
  
  // Mock chart data
  const chartData: ChartData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    data: [65, 78, 90, 81, 56, 95],
    type: chartType
  };

  const maxValue = Math.max(...chartData.data);
  const isCompact = widget.position.w === 2 && widget.position.h === 2;

  const renderBarChart = () => (
    <div className="flex items-end justify-between h-32 gap-1">
      {chartData.data.map((value, index) => (
        <div key={index} className="flex flex-col items-center flex-1">
          <div
            className="bg-primary rounded-t w-full transition-all duration-300 hover:bg-primary/80"
            style={{ height: `${(value / maxValue) * 100}%` }}
          />
          <div className="text-xs text-muted-foreground mt-1">
            {chartData.labels[index]}
          </div>
        </div>
      ))}
    </div>
  );

  const renderLineChart = () => (
    <div className="relative h-32">
      <svg className="w-full h-full" viewBox="0 0 300 120">
        <polyline
          fill="none"
          stroke="hsl(var(--primary))"
          strokeWidth="2"
          points={chartData.data.map((value, index) => 
            `${(index / (chartData.data.length - 1)) * 280 + 10},${120 - (value / maxValue) * 100}`
          ).join(' ')}
        />
        {chartData.data.map((value, index) => (
          <circle
            key={index}
            cx={(index / (chartData.data.length - 1)) * 280 + 10}
            cy={120 - (value / maxValue) * 100}
            r="3"
            fill="hsl(var(--primary))"
          />
        ))}
      </svg>
      <div className="flex justify-between mt-2">
        {chartData.labels.map((label, index) => (
          <div key={index} className="text-xs text-muted-foreground">
            {label}
          </div>
        ))}
      </div>
    </div>
  );

  const renderPieChart = () => {
    const total = chartData.data.reduce((sum, value) => sum + value, 0);
    let cumulativePercentage = 0;

    return (
      <div className="flex items-center justify-center h-32">
        <div className="relative w-24 h-24">
          <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
            {chartData.data.map((value, index) => {
              const percentage = (value / total) * 100;
              const strokeDasharray = `${percentage} ${100 - percentage}`;
              const strokeDashoffset = -cumulativePercentage;
              cumulativePercentage += percentage;

              return (
                <circle
                  key={index}
                  cx="50"
                  cy="50"
                  r="40"
                  fill="transparent"
                  stroke={`hsl(${(index * 60) % 360}, 70%, 50%)`}
                  strokeWidth="8"
                  strokeDasharray={strokeDasharray}
                  strokeDashoffset={strokeDashoffset}
                  className="transition-all duration-300"
                />
              );
            })}
          </svg>
        </div>
      </div>
    );
  };

  const getChartIcon = (type: string) => {
    switch (type) {
      case 'bar':
        return <BarChart3 className="h-4 w-4" />;
      case 'line':
        return <LineChart className="h-4 w-4" />;
      case 'pie':
        return <PieChart className="h-4 w-4" />;
      default:
        return <BarChart3 className="h-4 w-4" />;
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <TrendingUp className="h-4 w-4 text-primary" />
          <span className="text-sm font-medium">Analytics</span>
        </div>
        {!isCompact && (
          <div className="flex gap-1">
            {(['bar', 'line', 'pie'] as const).map((type) => (
              <Button
                key={type}
                variant={chartType === type ? "default" : "ghost"}
                size="sm"
                className="h-6 w-6 p-0"
                onClick={() => setChartType(type)}
              >
                {getChartIcon(type)}
              </Button>
            ))}
          </div>
        )}
      </div>

      {/* Chart */}
      <div className="flex-1">
        {chartType === 'bar' && renderBarChart()}
        {chartType === 'line' && renderLineChart()}
        {chartType === 'pie' && renderPieChart()}
      </div>

      {/* Stats */}
      {!isCompact && (
        <div className="mt-4 flex justify-between text-xs text-muted-foreground">
          <span>Avg: {Math.round(chartData.data.reduce((a, b) => a + b, 0) / chartData.data.length)}</span>
          <span>Max: {Math.max(...chartData.data)}</span>
        </div>
      )}
    </div>
  );
};

export default ChartWidget;

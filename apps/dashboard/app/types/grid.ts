export interface GridPosition {
  x: number;
  y: number;
  w: number; // width in grid units
  h: number; // height in grid units
}

export interface Widget {
  id: string;
  type: string;
  title: string;
  position: GridPosition;
  config?: Record<string, any>;
  minW?: number;
  minH?: number;
  maxW?: number;
  maxH?: number;
}

export interface GridConfig {
  cols: number;
  rows: number;
  cellSize: number;
  gap: number;
  containerPadding: number;
}

export interface GridSystemProps {
  widgets: Widget[];
  onWidgetsChange: (widgets: Widget[]) => void;
  gridConfig?: Partial<GridConfig>;
  className?: string;
}

export interface WidgetComponentProps {
  widget: Widget;
  onUpdate?: (widget: Widget) => void;
  onRemove?: (widgetId: string) => void;
}

export type WidgetType = 
  | 'collaborative-editor'
  | 'task-list'
  | 'notes'
  | 'calendar'
  | 'weather'
  | 'clock'
  | 'chart';

export interface WidgetDefinition {
  type: WidgetType;
  name: string;
  description: string;
  icon: React.ComponentType;
  defaultSize: { w: number; h: number };
  minSize: { w: number; h: number };
  maxSize?: { w: number; h: number };
  component: React.ComponentType<WidgetComponentProps>;
}

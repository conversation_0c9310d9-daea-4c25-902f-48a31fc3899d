"use client";

import { useEffect, useRef } from "react";

interface DotGridProps {
	theme: "light" | "dark" | "blue" | "green" | "polka";
}

const DotGrid: React.FC<DotGridProps> = ({ theme }) => {
	const canvasRef = useRef<HTMLCanvasElement>(null);

	useEffect(() => {
		const canvas = canvasRef.current;
		if (!canvas) return;

		const ctx = canvas.getContext("2d");
		if (!ctx) return;

		const resizeCanvas = () => {
			canvas.width = window.innerWidth;
			canvas.height = window.innerHeight;
			drawDots();
		};

		const drawDots = () => {
			if (!ctx) return;

			ctx.clearRect(0, 0, canvas.width, canvas.height);

			let dotSize = theme === "polka" ? 2 : 1;
			let spacing = theme === "polka" ? 30 : 20;
			let color;

			switch (theme) {
				case "dark":
					color = "rgba(255, 255, 255, 0.2)";
					break;
				case "blue":
					color = "rgba(37, 99, 235, 0.2)";
					break;
				case "green":
					color = "rgba(16, 185, 129, 0.2)";
					break;
				case "polka":
					color = "rgba(99, 102, 241, 0.15)";
					break;
				default:
					color = "rgba(0, 0, 0, 0.1)";
			}

			ctx.fillStyle = color;

			for (let x = 0; x < canvas.width; x += spacing) {
				for (let y = 0; y < canvas.height; y += spacing) {
					ctx.beginPath();
					ctx.arc(x, y, dotSize, 0, Math.PI * 2);
					ctx.fill();
				}
			}
		};

		resizeCanvas();
		window.addEventListener("resize", resizeCanvas);

		return () => {
			window.removeEventListener("resize", resizeCanvas);
		};
	}, [theme]);

	return (
		<canvas
			ref={canvasRef}
			className="fixed inset-0 -z-10 pointer-events-none"
			style={{
				backgroundColor:
					theme === "dark"
						? "#1a202c"
						: theme === "blue"
						? "#e6f2ff"
						: theme === "green"
						? "#e6fff2"
						: theme === "polka"
						? "#f5f3ff"
						: "#ffffff",
			}}
		/>
	);
};

export default DotGrid;

"use client";

import React, { useState, useCallback, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Plus, Trash2 } from "lucide-react";
import { Button } from "./ui/button";
import { Widget, GridConfig, GridSystemProps } from "../types/grid";
import GridWidget from "./GridWidget";
import WidgetLibrary from "./WidgetLibrary";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from "./ui/dialog";

const defaultGridConfig: GridConfig = {
	cols: 6,
	rows: 5,
	cellSize: 140,
	gap: 12,
	containerPadding: 20,
};

export default function GridSystem({
	widgets,
	onWidgetsChange,
	gridConfig = {},
	className = "",
}: GridSystemProps) {
	const [isLibraryOpen, setIsLibraryOpen] = useState(false);
	const [selectedWidget, setSelectedWidget] = useState<string | null>(null);
	const [dragPreview, setDragPreview] = useState<{
		widget: Widget;
		position: { x: number; y: number };
	} | null>(null);
	const [resizePreview, setResizePreview] = useState<{
		widget: Widget;
		position: { x: number; y: number; w: number; h: number };
	} | null>(null);
	const [resizingWidgets, setResizingWidgets] = useState<Set<string>>(
		new Set()
	);

	const config = { ...defaultGridConfig, ...gridConfig };

	// Global event listeners to ensure snapping always happens
	useEffect(() => {
		let snapTimeout: NodeJS.Timeout;

		const handleGlobalMouseUp = () => {
			if (dragPreview) {
				// Clear any existing timeout
				clearTimeout(snapTimeout);

				// Force snap the currently dragged widget
				const widget = dragPreview.widget;
				const snapped = dragPreview.position;

				const updatedWidgets = widgets.map((w) =>
					w.id === widget.id
						? { ...w, position: { ...w.position, x: snapped.x, y: snapped.y } }
						: w
				);
				onWidgetsChange(updatedWidgets);
				setDragPreview(null);
			}
		};

		const handleGlobalTouchEnd = () => {
			if (dragPreview) {
				// Clear any existing timeout
				clearTimeout(snapTimeout);

				// Force snap the currently dragged widget
				const widget = dragPreview.widget;
				const snapped = dragPreview.position;

				const updatedWidgets = widgets.map((w) =>
					w.id === widget.id
						? { ...w, position: { ...w.position, x: snapped.x, y: snapped.y } }
						: w
				);
				onWidgetsChange(updatedWidgets);
				setDragPreview(null);
			}
		};

		// Fallback: If drag preview exists for too long, force snap
		if (dragPreview) {
			snapTimeout = setTimeout(() => {
				const widget = dragPreview.widget;
				const snapped = dragPreview.position;

				const updatedWidgets = widgets.map((w) =>
					w.id === widget.id
						? { ...w, position: { ...w.position, x: snapped.x, y: snapped.y } }
						: w
				);
				onWidgetsChange(updatedWidgets);
				setDragPreview(null);
			}, 1000); // Force snap after 1 second if no release detected
		}

		// Add global event listeners
		document.addEventListener("mouseup", handleGlobalMouseUp);
		document.addEventListener("touchend", handleGlobalTouchEnd);

		// Cleanup
		return () => {
			clearTimeout(snapTimeout);
			document.removeEventListener("mouseup", handleGlobalMouseUp);
			document.removeEventListener("touchend", handleGlobalTouchEnd);
		};
	}, [dragPreview, widgets, onWidgetsChange]);

	// Calculate grid space consumption
	const totalGridSpaces = config.cols * config.rows;
	const occupiedSpaces = widgets.reduce((total, widget) => {
		return total + widget.position.w * widget.position.h;
	}, 0);
	const occupancyPercentage = Math.round(
		(occupiedSpaces / totalGridSpaces) * 100
	);

	// Calculate grid cell position (for individual cells)
	const getCellPosition = (col: number, row: number) => {
		return {
			x: col * (config.cellSize + config.gap) + config.containerPadding,
			y: row * (config.cellSize + config.gap) + config.containerPadding,
		};
	};

	// Calculate widget position and size (spans multiple cells)
	const getWidgetStyle = (widget: Widget) => {
		const startPos = getCellPosition(widget.position.x, widget.position.y);

		// Widget should exactly match grid cells with no gaps
		const width =
			widget.position.w * config.cellSize +
			(widget.position.w - 1) * config.gap;
		const height =
			widget.position.h * config.cellSize +
			(widget.position.h - 1) * config.gap;

		return {
			x: startPos.x,
			y: startPos.y,
			width,
			height,
		};
	};

	// Check if a position is occupied by any widget
	const isPositionOccupied = (
		x: number,
		y: number,
		excludeWidgetId?: string
	) => {
		return widgets.some((widget) => {
			if (excludeWidgetId && widget.id === excludeWidgetId) return false;

			const { position } = widget;
			return (
				x >= position.x &&
				x < position.x + position.w &&
				y >= position.y &&
				y < position.y + position.h
			);
		});
	};

	// Check if a widget can fit at a specific position
	const canWidgetFitAt = (
		x: number,
		y: number,
		w: number,
		h: number,
		excludeWidgetId?: string
	) => {
		// Check bounds
		if (x < 0 || y < 0 || x + w > config.cols || y + h > config.rows) {
			return false;
		}

		// Check for collisions with other widgets
		for (let dx = 0; dx < w; dx++) {
			for (let dy = 0; dy < h; dy++) {
				if (isPositionOccupied(x + dx, y + dy, excludeWidgetId)) {
					return false;
				}
			}
		}
		return true;
	};

	// Find the nearest valid position for a widget
	const findNearestValidPosition = (
		targetX: number,
		targetY: number,
		w: number,
		h: number,
		excludeWidgetId?: string
	) => {
		// Clamp to grid bounds first
		const clampedX = Math.max(0, Math.min(targetX, config.cols - w));
		const clampedY = Math.max(0, Math.min(targetY, config.rows - h));

		// If the clamped position is valid, use it
		if (canWidgetFitAt(clampedX, clampedY, w, h, excludeWidgetId)) {
			return { x: clampedX, y: clampedY };
		}

		// Search in expanding circles around the target position
		const maxDistance = Math.max(config.cols, config.rows);

		for (let distance = 1; distance <= maxDistance; distance++) {
			// Check positions in a square pattern around the target
			for (let dx = -distance; dx <= distance; dx++) {
				for (let dy = -distance; dy <= distance; dy++) {
					// Only check the perimeter of the current distance
					if (Math.abs(dx) !== distance && Math.abs(dy) !== distance) continue;

					const testX = clampedX + dx;
					const testY = clampedY + dy;

					if (canWidgetFitAt(testX, testY, w, h, excludeWidgetId)) {
						return { x: testX, y: testY };
					}
				}
			}
		}

		// If no valid position found, try to find any available position
		for (let y = 0; y <= config.rows - h; y++) {
			for (let x = 0; x <= config.cols - w; x++) {
				if (canWidgetFitAt(x, y, w, h, excludeWidgetId)) {
					return { x, y };
				}
			}
		}

		// Fallback to original position if nothing else works
		return { x: clampedX, y: clampedY };
	};

	// Snap position to grid with collision detection
	const snapToGrid = (x: number, y: number, widget: Widget) => {
		const cellX = Math.max(
			0,
			Math.min(
				Math.round(
					(x - config.containerPadding) / (config.cellSize + config.gap)
				),
				config.cols - widget.position.w
			)
		);
		const cellY = Math.max(
			0,
			Math.min(
				Math.round(
					(y - config.containerPadding) / (config.cellSize + config.gap)
				),
				config.rows - widget.position.h
			)
		);

		// First try the exact position where user is dragging
		if (
			canWidgetFitAt(
				cellX,
				cellY,
				widget.position.w,
				widget.position.h,
				widget.id
			)
		) {
			return { x: cellX, y: cellY };
		}

		// If not available, find nearest valid position
		return findNearestValidPosition(
			cellX,
			cellY,
			widget.position.w,
			widget.position.h,
			widget.id
		);
	};

	// Handle widget drag start
	const handleDragStart = useCallback(
		(widgetId: string) => {
			const widget = widgets.find((w) => w.id === widgetId);
			if (widget) {
				setDragPreview({
					widget,
					position: { x: widget.position.x, y: widget.position.y },
				});
			}
		},
		[widgets]
	);

	// Handle widget drag
	const handleWidgetDrag = useCallback(
		(widgetId: string, info: any) => {
			const widget = widgets.find((w) => w.id === widgetId);
			if (!widget) return;

			const currentStyle = getWidgetStyle(widget);
			const newX = currentStyle.x + info.offset.x;
			const newY = currentStyle.y + info.offset.y;
			const snapped = snapToGrid(newX, newY, widget);

			// Update drag preview to show where it will snap
			setDragPreview({ widget, position: snapped });
		},
		[widgets, getWidgetStyle, snapToGrid]
	);

	// Handle widget drag end
	const handleDragEnd = useCallback(
		(widgetId: string, info: any) => {
			const widget = widgets.find((w) => w.id === widgetId);
			if (!widget) return;

			const currentStyle = getWidgetStyle(widget);
			const newX = currentStyle.x + info.offset.x;
			const newY = currentStyle.y + info.offset.y;
			const snapped = snapToGrid(newX, newY, widget);

			// Update widget position to snapped grid position
			const updatedWidgets = widgets.map((w) =>
				w.id === widgetId
					? { ...w, position: { ...w.position, x: snapped.x, y: snapped.y } }
					: w
			);

			onWidgetsChange(updatedWidgets);
			setDragPreview(null);
		},
		[widgets, onWidgetsChange, getWidgetStyle, snapToGrid]
	);

	// Handle widget addition
	const handleAddWidget = useCallback(
		(
			widgetType: string,
			title: string,
			defaultSize: { w: number; h: number }
		) => {
			// Find the first available position for the new widget
			const position = findNearestValidPosition(
				0,
				0,
				defaultSize.w,
				defaultSize.h
			);

			const newWidget: Widget = {
				id: `widget-${Date.now()}`,
				type: widgetType,
				title,
				position: { ...position, w: defaultSize.w, h: defaultSize.h },
			};

			onWidgetsChange([...widgets, newWidget]);
			setIsLibraryOpen(false);
		},
		[widgets, onWidgetsChange, findNearestValidPosition]
	);

	// Handle widget removal
	const handleRemoveWidget = useCallback(
		(widgetId: string) => {
			onWidgetsChange(widgets.filter((w) => w.id !== widgetId));
			setSelectedWidget(null);
		},
		[widgets, onWidgetsChange]
	);

	// Handle widget selection
	const handleWidgetClick = useCallback(
		(widgetId: string) => {
			setSelectedWidget(selectedWidget === widgetId ? null : widgetId);
		},
		[selectedWidget]
	);

	// Handle widget resize
	const handleWidgetResize = useCallback(
		(widgetId: string, size: { width: number; height: number }) => {
			const updatedWidgets = widgets.map((w) =>
				w.id === widgetId
					? { ...w, position: { ...w.position, w: size.width, h: size.height } }
					: w
			);
			onWidgetsChange(updatedWidgets);
		},
		[widgets, onWidgetsChange]
	);

	// Handle resize state change
	const handleResizeStateChange = useCallback(
		(widgetId: string, isResizing: boolean) => {
			setResizingWidgets((prev) => {
				const newSet = new Set(prev);
				if (isResizing) {
					newSet.add(widgetId);
				} else {
					newSet.delete(widgetId);
					// Clear resize preview when resize ends
					setResizePreview(null);
				}
				return newSet;
			});
		},
		[]
	);

	// Handle resize preview updates with collision detection
	const handleResizePreview = useCallback(
		(
			widgetId: string,
			newSize: { w: number; h: number },
			direction: string
		) => {
			const widget = widgets.find((w) => w.id === widgetId);
			if (!widget) return;

			const currentPos = widget.position;
			let newPosition = { x: currentPos.x, y: currentPos.y };

			// Calculate anchor point based on resize direction
			if (direction.includes("left")) {
				newPosition.x = currentPos.x + currentPos.w - newSize.w;
			}
			if (direction.includes("top")) {
				newPosition.y = currentPos.y + currentPos.h - newSize.h;
			}

			// Ensure position stays within grid bounds
			newPosition.x = Math.max(
				0,
				Math.min(newPosition.x, config.cols - newSize.w)
			);
			newPosition.y = Math.max(0, newPosition.y);

			// Check if the new size and position would cause collisions
			const canFit = canWidgetFitAt(
				newPosition.x,
				newPosition.y,
				newSize.w,
				newSize.h,
				widget.id // Exclude the widget being resized from collision check
			);

			// Only show preview if the resize is valid (no collisions)
			if (canFit) {
				setResizePreview({
					widget,
					position: {
						x: newPosition.x,
						y: newPosition.y,
						w: newSize.w,
						h: newSize.h,
					},
				});
			} else {
				// Clear preview if resize would cause collision
				setResizePreview(null);
			}
		},
		[widgets, config.cols, canWidgetFitAt]
	);

	// Handle resize validation (check for collisions)
	const handleResizeValidation = useCallback(
		(
			widgetId: string,
			newSize: { w: number; h: number },
			direction: string
		) => {
			const widget = widgets.find((w) => w.id === widgetId);
			if (!widget) return false;

			const currentPos = widget.position;
			let newPosition = { x: currentPos.x, y: currentPos.y };

			// Calculate anchor point based on resize direction
			if (direction.includes("left")) {
				newPosition.x = currentPos.x + currentPos.w - newSize.w;
			}
			if (direction.includes("top")) {
				newPosition.y = currentPos.y + currentPos.h - newSize.h;
			}

			// Ensure position stays within grid bounds
			newPosition.x = Math.max(
				0,
				Math.min(newPosition.x, config.cols - newSize.w)
			);
			newPosition.y = Math.max(0, newPosition.y);

			// Check if the new size and position would cause collisions
			return canWidgetFitAt(
				newPosition.x,
				newPosition.y,
				newSize.w,
				newSize.h,
				widget.id // Exclude the widget being resized from collision check
			);
		},
		[widgets, config.cols, canWidgetFitAt]
	);

	const gridWidth =
		config.cols * (config.cellSize + config.gap) -
		config.gap +
		config.containerPadding * 2;
	const gridHeight =
		config.rows * (config.cellSize + config.gap) -
		config.gap +
		config.containerPadding * 2;

	// Ensure grid fits within viewport
	const maxAvailableHeight =
		typeof window !== "undefined" ? window.innerHeight - 120 : gridHeight; // 120px for WallManager + padding (80px height + 16px bottom + 24px buffer)
	const maxAvailableWidth =
		typeof window !== "undefined" ? window.innerWidth - 32 : gridWidth; // 32px for padding

	const finalGridWidth = Math.min(gridWidth, maxAvailableWidth);
	const finalGridHeight = Math.min(gridHeight, maxAvailableHeight);

	return (
		<div
			className={`relative h-full p-4 flex items-center justify-center overflow-hidden ${className}`}
			style={{
				paddingBottom: "120px", // Space for WallManager (80px height + 16px bottom + 24px buffer)
			}}
		>
			<div
				className="relative"
				style={{
					width: finalGridWidth,
					height: finalGridHeight,
					maxWidth: "100%",
					maxHeight: "100%",
				}}
			>
				{/* Grid Background */}
				<div
					className="absolute inset-0 opacity-10 pointer-events-none"
					style={{
						backgroundImage: `
	            linear-gradient(to right, hsl(var(--border)) 1px, transparent 1px),
	            linear-gradient(to bottom, hsl(var(--border)) 1px, transparent 1px)
	          `,
						backgroundSize: `${config.cellSize + config.gap}px ${config.cellSize + config.gap}px`,
						backgroundPosition: `${config.containerPadding}px ${config.containerPadding}px`,
					}}
				/>

				{/* Grid Cell Indicators */}
				<div className="absolute inset-0 pointer-events-none">
					{Array.from({ length: config.rows }, (_, row) =>
						Array.from({ length: config.cols }, (_, col) => {
							const isOccupied = isPositionOccupied(col, row);
							const cellPos = getCellPosition(col, row);

							return (
								<div
									key={`${col}-${row}`}
									className={`absolute border-2 rounded-md transition-all duration-200 ${
										isOccupied
											? "bg-primary/10 border-primary/30"
											: "border-border/30 hover:border-primary/50 hover:bg-primary/5"
									}`}
									style={{
										left: cellPos.x,
										top: cellPos.y,
										width: config.cellSize,
										height: config.cellSize,
									}}
								>
									{/* Grid coordinates for debugging */}
									<div className="absolute top-1 left-1 text-xs text-muted-foreground/50 font-mono">
										{col},{row}
									</div>
								</div>
							);
						})
					)}
				</div>

				{/* Toolbar */}
				<div className="fixed top-20 right-4 z-50 flex flex-col gap-2">
					{/* Space Consumption Indicator */}
					<div className="bg-card border border-border rounded-lg p-3 shadow-lg">
						<div className="text-xs font-medium text-foreground mb-2">
							Grid Usage
						</div>
						<div className="flex items-center gap-2">
							<div className="flex-1 bg-muted rounded-full h-2 overflow-hidden">
								<div
									className="h-full bg-primary transition-all duration-300"
									style={{ width: `${occupancyPercentage}%` }}
								/>
							</div>
							<div className="text-xs text-muted-foreground min-w-[3rem]">
								{occupancyPercentage}%
							</div>
						</div>
						<div className="text-xs text-muted-foreground mt-1">
							{occupiedSpaces}/{totalGridSpaces} spaces
						</div>
					</div>

					<Dialog open={isLibraryOpen} onOpenChange={setIsLibraryOpen}>
						<DialogTrigger asChild>
							<Button size="icon" className="shadow-lg">
								<Plus className="h-4 w-4" />
							</Button>
						</DialogTrigger>
						<DialogContent className="max-w-4xl">
							<DialogHeader>
								<DialogTitle>Add Widget</DialogTitle>
							</DialogHeader>
							<WidgetLibrary onAddWidget={handleAddWidget} />
						</DialogContent>
					</Dialog>

					{selectedWidget && (
						<motion.div
							initial={{ opacity: 0, scale: 0.8 }}
							animate={{ opacity: 1, scale: 1 }}
							exit={{ opacity: 0, scale: 0.8 }}
						>
							<Button
								size="icon"
								variant="destructive"
								className="shadow-lg"
								onClick={() => handleRemoveWidget(selectedWidget)}
							>
								<Trash2 className="h-4 w-4" />
							</Button>
						</motion.div>
					)}
				</div>

				{/* Grid Layout */}
				<div className="absolute inset-0">
					<AnimatePresence>
						{widgets.map((widget) => {
							const style = getWidgetStyle(widget);
							const isWidgetResizing = resizingWidgets.has(widget.id);

							return (
								<motion.div
									key={`${widget.id}-${widget.position.x}-${widget.position.y}`}
									drag={!isWidgetResizing}
									dragMomentum={false}
									dragElastic={0}
									onDragStart={() =>
										!isWidgetResizing && handleDragStart(widget.id)
									}
									onDrag={(_, info) =>
										!isWidgetResizing && handleWidgetDrag(widget.id, info)
									}
									onDragEnd={(_, info) =>
										!isWidgetResizing && handleDragEnd(widget.id, info)
									}
									style={{
										position: "absolute",
										left: style.x,
										top: style.y,
										width: style.width,
										height: style.height,
										zIndex: dragPreview?.widget.id === widget.id ? 1000 : 1,
									}}
									initial={{ opacity: 0, scale: 0.8 }}
									animate={{
										opacity: dragPreview?.widget.id === widget.id ? 0.7 : 1,
										scale: 1,
										x: 0,
										y: 0,
									}}
									exit={{ opacity: 0, scale: 0.8 }}
									transition={{
										type: "spring",
										stiffness: 300,
										damping: 30,
										x: { type: "spring", stiffness: 500, damping: 30 },
										y: { type: "spring", stiffness: 500, damping: 30 },
									}}
									whileHover={{ scale: 1.02 }}
									whileDrag={{ scale: 1.05 }}
								>
									<GridWidget
										widget={widget}
										isSelected={selectedWidget === widget.id}
										onClick={() => handleWidgetClick(widget.id)}
										onUpdate={(updatedWidget) => {
											const updatedWidgets = widgets.map((w) =>
												w.id === updatedWidget.id ? updatedWidget : w
											);
											onWidgetsChange(updatedWidgets);
										}}
										onRemove={handleRemoveWidget}
										onDrag={handleWidgetDrag}
										onResize={handleWidgetResize}
										onResizeStateChange={handleResizeStateChange}
										onResizePreview={handleResizePreview}
										onResizeValidation={handleResizeValidation}
										gridConfig={config}
									/>
								</motion.div>
							);
						})}
					</AnimatePresence>

					{/* Drag Preview */}
					{dragPreview && (
						<motion.div
							className="absolute pointer-events-none border-2 border-primary border-dashed bg-primary/10 rounded-lg"
							style={{
								left: getWidgetStyle({
									...dragPreview.widget,
									position: {
										...dragPreview.widget.position,
										...dragPreview.position,
									},
								}).x,
								top: getWidgetStyle({
									...dragPreview.widget,
									position: {
										...dragPreview.widget.position,
										...dragPreview.position,
									},
								}).y,
								width: getWidgetStyle(dragPreview.widget).width,
								height: getWidgetStyle(dragPreview.widget).height,
								zIndex: 999,
							}}
							initial={{ opacity: 0 }}
							animate={{ opacity: 1 }}
							exit={{ opacity: 0 }}
						>
							<div className="absolute inset-0 flex items-center justify-center">
								<div className="bg-primary text-primary-foreground px-2 py-1 rounded text-xs font-medium">
									{dragPreview.position.x},{dragPreview.position.y}
								</div>
							</div>
						</motion.div>
					)}

					{/* Resize Preview */}
					{resizePreview && (
						<motion.div
							className="absolute pointer-events-none border-2 border-primary border-dashed bg-primary/10 rounded-lg"
							style={{
								left: getWidgetStyle({
									...resizePreview.widget,
									position: resizePreview.position,
								}).x,
								top: getWidgetStyle({
									...resizePreview.widget,
									position: resizePreview.position,
								}).y,
								width: getWidgetStyle({
									...resizePreview.widget,
									position: resizePreview.position,
								}).width,
								height: getWidgetStyle({
									...resizePreview.widget,
									position: resizePreview.position,
								}).height,
								zIndex: 999,
							}}
							initial={{ opacity: 0 }}
							animate={{ opacity: 1 }}
							exit={{ opacity: 0 }}
						>
							<div className="absolute inset-0 flex items-center justify-center">
								<div className="bg-primary text-primary-foreground px-2 py-1 rounded text-xs font-medium">
									{resizePreview.position.w}×{resizePreview.position.h}
								</div>
							</div>
						</motion.div>
					)}
				</div>

				{/* Empty State */}
				{widgets.length === 0 && (
					<motion.div
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						className="absolute inset-0 flex items-center justify-center"
					>
						<div className="text-center">
							<div className="text-6xl mb-4">📱</div>
							<h2 className="text-2xl font-bold text-foreground mb-2">
								Your Dashboard is Empty
							</h2>
							<p className="text-muted-foreground mb-6">
								Add your first widget to get started
							</p>
							<Button onClick={() => setIsLibraryOpen(true)} size="lg">
								<Plus className="h-4 w-4 mr-2" />
								Add Widget
							</Button>
						</div>
					</motion.div>
				)}
			</div>
		</div>
	);
}

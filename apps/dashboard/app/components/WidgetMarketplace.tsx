import React from "react";
import {
	<PERSON>,
	<PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	Card<PERSON>itle,
} from "@/app/components/ui/card";
import { But<PERSON> } from "@/app/components/ui/button";
import {
	Clock,
	Calendar,
	BarChart2,
	Thermometer,
	Globe,
	Clipboard,
} from "lucide-react";

interface Widget {
	id: string;
	component: React.ReactNode;
	position: { x: number; y: number };
}

const widgets: Omit<Widget, "position">[] = [
	{ id: "clock", component: <ClockWidget /> },
	{ id: "calendar", component: <CalendarWidget /> },
	{ id: "chart", component: <ChartWidget /> },
	{ id: "worldclock", component: <WorldClockWidget /> },
	{ id: "notes", component: <NotesWidget /> },
];

function ClockWidget() {
	const [time, setTime] = React.useState(new Date());

	React.useEffect(() => {
		const timer = setInterval(() => setTime(new Date()), 1000);
		return () => clearInterval(timer);
	}, []);

	return <div className="text-lg font-bold">{time.toLocaleTimeString()}</div>;
}

function CalendarWidget() {
	const today = new Date();
	return (
		<div className="text-sm">
			{today.toLocaleDateString(undefined, {
				weekday: "short",
				month: "short",
				day: "numeric",
			})}
		</div>
	);
}

function ChartWidget() {
	return (
		<div className="flex items-end h-8 space-x-1">
			<div className="w-1 bg-blue-500 h-3" />
			<div className="w-1 bg-blue-500 h-5" />
			<div className="w-1 bg-blue-500 h-4" />
			<div className="w-1 bg-blue-500 h-6" />
			<div className="w-1 bg-blue-500 h-2" />
		</div>
	);
}

function WeatherWidget() {
	return (
		<div className="flex items-center space-x-2">
			<Thermometer className="w-4 h-4" />
			<span className="text-sm">23°C</span>
		</div>
	);
}

function WorldClockWidget() {
	return (
		<div className="text-xs">
			<div>New York: 09:00</div>
			<div>London: 14:00</div>
			<div>Tokyo: 22:00</div>
		</div>
	);
}

function NotesWidget() {
	return <div className="text-xs italic">"Remember to..."</div>;
}

interface WidgetMarketplaceProps {
	onAddWidget: (widget: {
		id: string;
		component: React.ReactNode;
		position: { x: number; y: number };
	}) => void;
}

const WidgetMarketplace: React.FC<WidgetMarketplaceProps> = ({
	onAddWidget,
}) => {
	return (
		<div className="grid grid-cols-1 gap-3">
			{widgets.map((widget) => (
				<Card key={widget.id} className="cursor-move">
					<CardHeader className="p-3">
						<CardTitle className="text-sm flex items-center space-x-2">
							{widget.id === "clock" ? (
								<Clock className="w-4 h-4" />
							) : widget.id === "calendar" ? (
								<Calendar className="w-4 h-4" />
							) : widget.id === "chart" ? (
								<BarChart2 className="w-4 h-4" />
							) : widget.id === "worldclock" ? (
								<Globe className="w-4 h-4" />
							) : widget.id === "notes" ? (
								<Clipboard className="w-4 h-4" />
							) : null}
							<span>
								{widget.id === "clock"
									? "Clock"
									: widget.id === "calendar"
									? "Calendar"
									: widget.id === "chart"
									? "Chart"
									: widget.id === "worldclock"
									? "World Clock"
									: widget.id === "notes"
									? "Quick Notes"
									: null}
							</span>
						</CardTitle>
						<CardDescription className="text-xs">
							{widget.id === "clock"
								? "Current time"
								: widget.id === "calendar"
								? "Today's date"
								: widget.id === "chart"
								? "Simple bar chart"
								: widget.id === "worldclock"
								? "Time zones"
								: widget.id === "notes"
								? "Jot down ideas"
								: null}
						</CardDescription>
					</CardHeader>
					<CardContent className="p-3">
						<div className="bg-gray-100 dark:bg-gray-800 p-2 rounded-md h-16 flex items-center justify-center">
							{widget.component}
						</div>
					</CardContent>
					<CardFooter className="p-2">
						<Button
							onClick={() =>
                                onAddWidget({
                                    id: widget.id,
                                    component: widget.component,
                                    // Fixed deterministic position to avoid hydration errors
                                    position: { x: 100, y: 100 },
                                })
                            }
							className="w-full h-7 text-xs"
						>
							Add Widget
						</Button>
					</CardFooter>
				</Card>
			))}
		</div>
	);
};

export default WidgetMarketplace;

"use client";

import React, { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Search, Command } from "lucide-react";
import { Input } from "@/app/components/ui/input";

interface SearchPillProps {
	onSearch: (query: string) => void;
}

const SearchPill: React.FC<SearchPillProps> = ({ onSearch }) => {
	const [isExpanded, setIsExpanded] = useState(false);
	const [searchQuery, setSearchQuery] = useState("");
	const [isDropdownOpen, setIsDropdownOpen] = useState(false);
	const [isFocused, setIsFocused] = useState(false); // Update 1: Added isFocused state
	const inputRef = useRef<HTMLInputElement>(null);

	useEffect(() => {
		const handleKeyDown = (event: KeyboardEvent) => {
			if (event.key === "/") {
				event.preventDefault();
				setIsExpanded(true);
				setIsDropdownOpen(true);
			} else if (event.key === "Escape") {
				setIsExpanded(false);
				setIsDropdownOpen(false);
				setSearchQuery("");
			}
		};

		window.addEventListener("keydown", handleKeyDown);
		return () => window.removeEventListener("keydown", handleKeyDown);
	}, []);

	useEffect(() => {
		if (isExpanded && inputRef.current) {
			inputRef.current.focus();
		}
	}, [isExpanded]);

	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			const target = event.target as HTMLElement;
			if (!target.closest(".search-pill-container")) {
				setIsExpanded(false);
				setIsDropdownOpen(false);
				setIsFocused(false);
			}
		};

		document.addEventListener("click", handleClickOutside);
		return () => {
			document.removeEventListener("click", handleClickOutside);
		};
	}, []);

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		onSearch(searchQuery);
	};

	const handlePillClick = () => {
		setIsExpanded(true);
		setIsDropdownOpen(true);
		setIsFocused(true); // Update 3: Added setIsFocused(true)
	};

	const itemVariants = {
		hidden: { opacity: 0, y: 5 },
		visible: (i: number) => ({
			opacity: 1,
			y: 0,
			transition: { delay: i * 0.05 },
		}),
	};

	return (
		<div className="fixed inset-x-0 bottom-28 flex justify-center items-center z-50">
			<motion.div
				initial={false}
				animate={{
					width: isExpanded ? "300px" : "180px", // Increased from 120px to 180px
					height: "40px",
				}}
				className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-full shadow-lg search-pill-container overflow-hidden"
			>
				<AnimatePresence>
					<motion.form
						initial={{ opacity: 0 }}
						animate={{ opacity: 1 }}
						exit={{ opacity: 0 }}
						className="flex items-center px-3 py-1.5 h-full"
						onSubmit={handleSubmit}
					>
						<Search className="w-5 h-5 text-gray-400 mr-2 flex-shrink-0" />
						{isExpanded ? (
							<Input
								ref={inputRef}
								type="text"
								placeholder={
									isFocused || searchQuery
										? "Search or type a command"
										: "Search"
								}
								className={`bg-transparent border-none focus:outline-none focus:ring-0 text-sm w-full pr-8 ${
									!isFocused && !searchQuery
										? "placeholder-gray-400 dark:placeholder-gray-500"
										: ""
								}`}
								style={{ boxShadow: "none" }}
								value={searchQuery}
								onChange={(e) => setSearchQuery(e.target.value)}
								onClick={handlePillClick}
								onFocus={() => setIsFocused(true)}
								onBlur={() => setIsFocused(false)}
							/>
						) : (
							<>
								<span className="text-sm text-gray-500 dark:text-gray-400 mr-auto">
									Search
								</span>
								<div className="flex items-center bg-gray-100 dark:bg-gray-700 rounded px-1.5 py-0.5 text-xs text-gray-500 dark:text-gray-400">
									<Command className="w-3 h-3 mr-0.5" />
									<span>/</span>
								</div>
							</>
						)}
					</motion.form>
				</AnimatePresence>
				{isDropdownOpen && (
					<motion.div
						initial={{ opacity: 0, y: 10 }}
						animate={{ opacity: 1, y: 0 }}
						exit={{ opacity: 0, y: 10 }}
						className="absolute left-[calc(50%-0.5rem)] transform -translate-x-1/2 bottom-full w-64 mb-1 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg shadow-lg p-4"
					>
						<div className="text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
							Quick Actions
						</div>
						<ul className="space-y-1">
							{[
								"Create Task",
								"Add Note",
								"Schedule Event",
								"Start Timer",
								"Open Dashboard",
							].map((action, index) => (
								<motion.li
									key={index}
									variants={itemVariants}
									initial="hidden"
									animate="visible"
									custom={index}
									className="px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md cursor-pointer transition-colors duration-150 ease-in-out text-gray-600 dark:text-gray-300"
								>
									{action}
								</motion.li>
							))}
						</ul>
					</motion.div>
				)}
			</motion.div>
		</div>
	);
};

export default SearchPill;

'use client'

import { useState } from 'react'
import { motion, PanInfo, useAnimation } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

interface SwipeableCardProps {
  id: string
  content: string
  color: string
}

export default function SwipeableCard({ id, content, color }: SwipeableCardProps) {
  const [isOpen, setIsOpen] = useState(false)
  const controls = useAnimation()

  const handleDragEnd = (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
    const threshold = 50
    if (info.offset.x > threshold) {
      setIsOpen(true)
      controls.start({ x: 100 })
    } else if (info.offset.x < -threshold) {
      setIsOpen(false)
      controls.start({ x: 0 })
    } else {
      controls.start({ x: 0 })
    }
  }

  return (
    <motion.div
      drag="x"
      dragConstraints={{ left: 0, right: 100 }}
      onDragEnd={handleDragEnd}
      animate={controls}
    >
      <Card className={`${color} shadow-lg`}>
        <CardHeader>
          <CardTitle>{content}</CardTitle>
        </CardHeader>
        <CardContent>
          <p>Content for {content}</p>
          {isOpen && (
            <div className="mt-4">
              <p>Additional content when swiped open</p>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  )
}


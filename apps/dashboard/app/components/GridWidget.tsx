"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { GripHorizontal, Settings, X, Maximize2 } from "lucide-react";
import { Resizable } from "re-resizable";
import { Widget } from "../types/grid";
import { Button } from "./ui/button";
import { cn } from "@/lib/utils";
import { isSizeSupported } from "../config/widgetSizes";

// Import widget components
import CollaborativeEditorWidget from "../widgets/CollaborativeEditorWidget";
import TaskListWidget from "../widgets/TaskListWidget";
import NotesWidget from "../widgets/NotesWidget";
import ClockWidget from "../widgets/ClockWidget";
import CalendarWidget from "../widgets/CalendarWidget";
import WeatherWidget from "../widgets/WeatherWidget";
import ChartWidget from "../widgets/ChartWidget";
import WebViewWidget from "../widgets/WebViewWidget";

interface GridWidgetProps {
	widget: Widget;
	isSelected?: boolean;
	onClick?: () => void;
	onUpdate?: (widget: Widget) => void;
	onRemove?: (widgetId: string) => void;
	onDrag?: (widgetId: string, info: any) => void;
	onDragStart?: () => void;
	onDragEnd?: (info: any) => void;
	onResize?: (
		widgetId: string,
		size: { width: number; height: number }
	) => void;
	onResizeStateChange?: (widgetId: string, isResizing: boolean) => void;
	onResizePreview?: (
		widgetId: string,
		newSize: { w: number; h: number },
		direction: string
	) => void;
	onResizeValidation?: (
		widgetId: string,
		newSize: { w: number; h: number },
		direction: string
	) => boolean;
	gridConfig?: {
		cellSize: number;
		gap: number;
		containerPadding: number;
		cols?: number;
		rows?: number;
	};
	isResizing?: boolean;
}

// Widget component registry
const WidgetComponents = {
	"collaborative-editor": CollaborativeEditorWidget,
	"task-list": TaskListWidget,
	notes: NotesWidget,
	clock: ClockWidget,
	calendar: CalendarWidget,
	weather: WeatherWidget,
	chart: ChartWidget,
	"web-view": WebViewWidget,
} as const;

export default function GridWidget({
	widget,
	isSelected = false,
	onClick,
	onUpdate,
	onRemove,
	onDrag,
	onDragStart,
	onDragEnd,
	onResize,
	onResizeStateChange,
	onResizePreview,
	onResizeValidation,
	gridConfig = {
		cellSize: 140,
		gap: 12,
		containerPadding: 20,
		cols: 7,
		rows: 5,
	},
	isResizing: isWidgetResizing = false,
}: GridWidgetProps) {
	const [isLocalResizing, setIsLocalResizing] = useState(false);
	const [currentSize, setCurrentSize] = useState({
		width: "100%",
		height: "100%",
	});
	const WidgetComponent =
		WidgetComponents[widget.type as keyof typeof WidgetComponents];

	if (!WidgetComponent) {
		return (
			<div className="w-full h-full bg-destructive/10 border border-destructive rounded-lg flex items-center justify-center">
				<p className="text-destructive text-sm">
					Unknown widget type: {widget.type}
				</p>
			</div>
		);
	}

	// Calculate grid-snapped size
	const snapSizeToGrid = (width: number, height: number) => {
		const cols = Math.max(
			1,
			Math.round(width / (gridConfig.cellSize + gridConfig.gap))
		);
		const rows = Math.max(
			1,
			Math.round(height / (gridConfig.cellSize + gridConfig.gap))
		);
		return { cols, rows };
	};

	// Handle resize start
	const handleResizeStart = () => {
		setIsLocalResizing(true);
		onResizeStateChange?.(widget.id, true);
	};

	// Handle real-time resize (during drag) - HARD snap to grid with no free-form
	const handleResizeDuring = (
		e: any,
		direction: string,
		ref: HTMLElement,
		delta: any
	) => {
		const newSize = snapSizeToGrid(ref.offsetWidth, ref.offsetHeight);

		// Check if the resize is valid (no collisions and supported size)
		const isSizeSupportedForWidget = isSizeSupported(widget.type as any, {
			w: newSize.cols,
			h: newSize.rows,
		});
		const isValidResize =
			isSizeSupportedForWidget &&
			(onResizeValidation?.(
				widget.id,
				{ w: newSize.cols, h: newSize.rows },
				direction
			) ??
				true); // Default to true if no validation function provided

		// Only proceed if resize is valid
		if (isValidResize) {
			// Calculate the actual pixel size based on grid snapped size
			const snappedWidth =
				newSize.cols * (gridConfig.cellSize + gridConfig.gap) - gridConfig.gap;
			const snappedHeight =
				newSize.rows * (gridConfig.cellSize + gridConfig.gap) - gridConfig.gap;

			// Update resize preview to show blue highlight
			onResizePreview?.(
				widget.id,
				{ w: newSize.cols, h: newSize.rows },
				direction
			);

			// Force the resizable to the exact snapped size - NO position updates during resize
			setCurrentSize({
				width: `${snappedWidth}px`,
				height: `${snappedHeight}px`,
			});
		} else {
			// If resize is invalid, revert to current widget size
			const currentWidth =
				widget.position.w * (gridConfig.cellSize + gridConfig.gap) -
				gridConfig.gap;
			const currentHeight =
				widget.position.h * (gridConfig.cellSize + gridConfig.gap) -
				gridConfig.gap;

			setCurrentSize({
				width: `${currentWidth}px`,
				height: `${currentHeight}px`,
			});
		}
	};

	// Handle resize end - calculate final position and update widget data
	const handleResizeEnd = (
		e: any,
		direction: string,
		ref: HTMLElement,
		delta: any
	) => {
		const newSize = snapSizeToGrid(ref.offsetWidth, ref.offsetHeight);

		// Check if the final resize is valid (no collisions and supported size)
		const isSizeSupportedForWidget = isSizeSupported(widget.type as any, {
			w: newSize.cols,
			h: newSize.rows,
		});
		const isValidResize =
			isSizeSupportedForWidget &&
			(onResizeValidation?.(
				widget.id,
				{ w: newSize.cols, h: newSize.rows },
				direction
			) ??
				true); // Default to true if no validation function provided

		// Only update widget data if resize is valid
		if (isValidResize) {
			const currentPos = widget.position;

			// Calculate new position based on resize direction (anchor point logic)
			let newPosition = { x: currentPos.x, y: currentPos.y };

			// Determine anchor point based on resize direction
			// The anchor point is the OPPOSITE side/corner from where you're dragging

			if (direction.includes("left")) {
				// Dragging from LEFT edge/corner -> anchor RIGHT side
				// Right edge stays at: currentPos.x + currentPos.w
				// New left edge should be: rightEdge - newWidth
				newPosition.x = currentPos.x + currentPos.w - newSize.cols;
			}
			// Note: Don't use else if - corners can have both left/right AND top/bottom

			if (direction.includes("top")) {
				// Dragging from TOP edge/corner -> anchor BOTTOM side
				// Bottom edge stays at: currentPos.y + currentPos.h
				// New top edge should be: bottomEdge - newHeight
				newPosition.y = currentPos.y + currentPos.h - newSize.rows;
			}

			// For right and bottom edges, position stays the same (anchor top-left)
			// This happens automatically since we start with currentPos.x, currentPos.y

			// Ensure position stays within grid bounds
			newPosition.x = Math.max(
				0,
				Math.min(newPosition.x, (gridConfig?.cols || 7) - newSize.cols)
			);
			newPosition.y = Math.max(0, newPosition.y);

			// Update both size and position
			const updatedWidget = {
				...widget,
				position: {
					...widget.position,
					x: newPosition.x,
					y: newPosition.y,
					w: newSize.cols,
					h: newSize.rows,
				},
			};

			// Update widget data only at the end
			onUpdate?.(updatedWidget);
		}

		// End resize state
		setIsLocalResizing(false);
		onResizeStateChange?.(widget.id, false);

		// Reset size state to percentage-based for normal display
		setCurrentSize({
			width: "100%",
			height: "100%",
		});
	};

	return (
		<motion.div
			layout
			initial={{ opacity: 0, scale: 0.8 }}
			animate={{ opacity: 1, scale: 1 }}
			exit={{ opacity: 0, scale: 0.8 }}
			transition={{ type: "spring", stiffness: 300, damping: 30 }}
			className="relative w-full h-full"
		>
			<Resizable
				size={currentSize}
				onResizeStart={handleResizeStart}
				onResize={handleResizeDuring}
				onResizeStop={handleResizeEnd}
				minWidth={gridConfig.cellSize}
				minHeight={gridConfig.cellSize}
				step={gridConfig.cellSize + gridConfig.gap}
				enable={{
					top: true,
					right: true,
					bottom: true,
					left: true,
					topRight: true,
					bottomRight: true,
					bottomLeft: true,
					topLeft: true,
				}}
				handleStyles={{
					// Corner handles (much larger for easier grabbing) - removed blue background
					topLeft: {
						background: "transparent",
						width: "32px",
						height: "32px",
						borderRadius: "8px 0 0 0",
						opacity: 1,
						cursor: "nw-resize",
						border: "none",
					},
					topRight: {
						background: "transparent",
						width: "32px",
						height: "32px",
						borderRadius: "0 8px 0 0",
						opacity: 1,
						cursor: "ne-resize",
						border: "none",
					},
					bottomLeft: {
						background: "transparent",
						width: "32px",
						height: "32px",
						borderRadius: "0 0 0 8px",
						opacity: 1,
						cursor: "sw-resize",
						border: "none",
					},
					bottomRight: {
						background: "transparent",
						width: "32px",
						height: "32px",
						borderRadius: "0 0 8px 0",
						opacity: 1,
						cursor: "se-resize",
						border: "none",
					},
					// Edge handles (thicker for easier grabbing) - removed blue background
					top: {
						background: "transparent",
						height: "6px",
						opacity: 1,
						cursor: "n-resize",
					},
					right: {
						background: "transparent",
						width: "6px",
						opacity: 1,
						cursor: "e-resize",
					},
					bottom: {
						background: "transparent",
						height: "6px",
						opacity: 1,
						cursor: "s-resize",
					},
					left: {
						background: "transparent",
						width: "6px",
						opacity: 1,
						cursor: "w-resize",
					},
				}}
				className={cn(
					"bg-card border rounded-md shadow-sm overflow-hidden group",
					"hover:shadow-md transition-shadow duration-200",
					isSelected &&
						"ring-2 ring-primary ring-offset-1 ring-offset-background border-primary"
				)}
				onClick={onClick}
				onMouseDown={(e) => {
					// Prevent drag when clicking on resize handles
					if (isLocalResizing || isWidgetResizing) {
						e.stopPropagation();
					}
				}}
			>
				{/* Widget Header - Always visible */}
				<div className="absolute top-0 left-0 right-0 z-10 bg-card/95 backdrop-blur-sm border-b border-border px-3 py-2 flex items-center justify-between opacity-100">
					<div className="flex items-center gap-2">
						<motion.div
							drag={!isWidgetResizing}
							dragMomentum={false}
							dragElastic={0}
							onDragStart={onDragStart}
							onDrag={(_, info) => onDrag?.(widget.id, info)}
							onDragEnd={(_, info) => onDragEnd?.(info)}
							className="cursor-move"
							whileDrag={{ scale: 1.1 }}
						>
							<GripHorizontal className="h-4 w-4 text-muted-foreground" />
						</motion.div>
						<span className="text-sm font-medium text-foreground truncate">
							{widget.title}
						</span>
					</div>

					<div className="flex items-center gap-1">
						<Button
							size="sm"
							variant="ghost"
							className="h-6 w-6 p-0"
							onClick={(e) => {
								e.stopPropagation();
								// TODO: Open widget settings
							}}
						>
							<Settings className="h-3 w-3" />
						</Button>
						<Button
							size="sm"
							variant="ghost"
							className="h-6 w-6 p-0 text-destructive hover:text-destructive"
							onClick={(e) => {
								e.stopPropagation();
								onRemove?.(widget.id);
							}}
						>
							<X className="h-3 w-3" />
						</Button>
					</div>
				</div>

				{/* Widget Content */}
				<div className="w-full h-full p-4 pt-6">
					<WidgetComponent
						widget={widget}
						onUpdate={onUpdate}
						onRemove={onRemove}
					/>
				</div>

				{/* Resize Indicator */}
				<div className="absolute bottom-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
					<Maximize2 className="h-3 w-3 text-muted-foreground" />
				</div>
			</Resizable>
		</motion.div>
	);
}

import React, { useState, useEffect } from 'react'

const cities = [
  { name: 'New York', timezone: 'America/New_York' },
  { name: 'London', timezone: 'Europe/London' },
  { name: 'Tokyo', timezone: 'Asia/Tokyo' },
]

export function WorldClockWidget() {
  const [times, setTimes] = useState<string[]>([])

  useEffect(() => {
    const updateTimes = () => {
      const newTimes = cities.map(city => {
        return new Date().toLocaleTimeString('en-US', { timeZone: city.timezone, timeStyle: 'short' })
      })
      setTimes(newTimes)
    }

    updateTimes()
    const timer = setInterval(updateTimes, 1000)
    return () => clearInterval(timer)
  }, [])

  return (
    <div className="space-y-2">
      {cities.map((city, index) => (
        <div key={city.name} className="flex justify-between">
          <span>{city.name}:</span>
          <span>{times[index]}</span>
        </div>
      ))}
    </div>
  )
}


"use client";

import { Tldraw } from "tldraw";
import "tldraw/tldraw.css";

export function TlDrawCollabWidget() {
	return (
		<div className="space-y-2" style={{ height: "300px" }}>
			<div className="w-full h-full border rounded overflow-hidden">
				<Tldraw
					autoFocus={false}
					persistenceKey="tldraw-widget"
					components={{
						ContextMenu: null,
						NavigationPanel: null,
						ActionsMenu: null,
						HelpMenu: null,
						StylePanel: null,
						PageMenu: null,
						MainMenu: null,
						MenuPanel: null,
					}}
				/>
			</div>
			<div className="text-xs text-gray-500 text-center">
				🎨 Tldraw Widget (Local Storage)
			</div>
		</div>
	);
}

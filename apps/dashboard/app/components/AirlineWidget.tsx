import { useState, useEffect } from 'react'

interface Flight {
  id: number
  from: string
  to: string
  price: string
  departureTime: string
}

export function AirlineWidget() {
  const [flights, setFlights] = useState<Flight[]>([])

  useEffect(() => {
    // Simulating API call
    const mockFlights: Flight[] = [
      { id: 1, from: 'NYC', to: 'LAX', price: '$199', departureTime: '9:00 AM' },
      { id: 2, from: 'SFO', to: 'CHI', price: '$149', departureTime: '11:30 AM' },
      { id: 3, from: 'MIA', to: 'SEA', price: '$279', departureTime: '2:15 PM' },
    ]
    setFlights(mockFlights)
  }, [])

  return (
    <div className="space-y-2">
      {flights.map((flight) => (
        <div key={flight.id} className="flex justify-between items-center text-sm">
          <span className="font-medium">{flight.from} to {flight.to}</span>
          <span className="text-gray-600 dark:text-gray-400">{flight.price} ({flight.departureTime})</span>
        </div>
      ))}
    </div>
  )
}


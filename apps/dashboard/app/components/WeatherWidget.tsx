import { useState, useEffect } from 'react'
import { Sun, Cloud, CloudRain } from 'lucide-react'

interface Weather {
  temperature: number
  condition: 'sunny' | 'cloudy' | 'rainy'
  location: string
}

export function WeatherWidget() {
  const [weather, setWeather] = useState<Weather | null>(null)

  useEffect(() => {
    // Simulating API call
    const mockWeather: Weather = {
      temperature: 72,
      condition: 'sunny',
      location: 'San Francisco, CA'
    }
    setWeather(mockWeather)
  }, [])

  if (!weather) return null

  const getWeatherIcon = (condition: Weather['condition']) => {
    switch (condition) {
      case 'sunny':
        return <Sun className="w-8 h-8 text-yellow-400" />
      case 'cloudy':
        return <Cloud className="w-8 h-8 text-gray-400" />
      case 'rainy':
        return <CloudRain className="w-8 h-8 text-blue-400" />
    }
  }

  return (
    <div className="flex items-center justify-between">
      <div>
        <h3 className="font-medium">{weather.location}</h3>
        <p className="text-2xl font-bold">{weather.temperature}°F</p>
      </div>
      {getWeatherIcon(weather.condition)}
    </div>
  )
}


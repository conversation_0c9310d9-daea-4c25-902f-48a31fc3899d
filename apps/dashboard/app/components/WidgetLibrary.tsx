"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { 
  FileText, 
  CheckSquare, 
  Clock, 
  Calendar,
  BarChart3,
  Thermometer,
  Globe,
  Edit3
} from 'lucide-react';
import { Button } from './ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';

interface WidgetDefinition {
  type: string;
  name: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  defaultSize: { w: number; h: number };
  category: string;
}

interface WidgetLibraryProps {
  onAddWidget: (type: string, title: string, defaultSize: { w: number; h: number }) => void;
}

const widgetDefinitions: WidgetDefinition[] = [
  {
    type: 'collaborative-editor',
    name: 'Collaborative Editor',
    description: 'Real-time collaborative text editor with shared tasks',
    icon: Edit3,
    defaultSize: { w: 4, h: 3 },
    category: 'Productivity',
  },
  {
    type: 'task-list',
    name: 'Task List',
    description: 'Simple task management with checkboxes',
    icon: CheckSquare,
    defaultSize: { w: 2, h: 3 },
    category: 'Productivity',
  },
  {
    type: 'notes',
    name: 'Quick Notes',
    description: 'Simple note-taking widget',
    icon: FileText,
    defaultSize: { w: 2, h: 2 },
    category: 'Productivity',
  },
  {
    type: 'clock',
    name: 'Clock',
    description: 'Digital clock with timezone support',
    icon: Clock,
    defaultSize: { w: 2, h: 1 },
    category: 'Utilities',
  },
  {
    type: 'calendar',
    name: 'Calendar',
    description: 'Monthly calendar view',
    icon: Calendar,
    defaultSize: { w: 3, h: 3 },
    category: 'Utilities',
  },
  {
    type: 'chart',
    name: 'Chart',
    description: 'Data visualization charts',
    icon: BarChart3,
    defaultSize: { w: 3, h: 2 },
    category: 'Analytics',
  },
  {
    type: 'weather',
    name: 'Weather',
    description: 'Current weather conditions',
    icon: Thermometer,
    defaultSize: { w: 2, h: 2 },
    category: 'Utilities',
  },
  {
    type: 'web-view',
    name: 'Web View',
    description: 'Embed web content',
    icon: Globe,
    defaultSize: { w: 3, h: 3 },
    category: 'Utilities',
  },
];

const categories = Array.from(new Set(widgetDefinitions.map(w => w.category)));

export default function WidgetLibrary({ onAddWidget }: WidgetLibraryProps) {
  return (
    <div className="space-y-6">
      {categories.map((category) => (
        <div key={category}>
          <h3 className="text-lg font-semibold text-foreground mb-3">{category}</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {widgetDefinitions
              .filter((widget) => widget.category === category)
              .map((widget) => (
                <motion.div
                  key={widget.type}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Card className="cursor-pointer hover:shadow-md transition-shadow duration-200">
                    <CardHeader className="pb-3">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-primary/10 rounded-lg">
                          <widget.icon className="h-5 w-5 text-primary" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <CardTitle className="text-sm font-medium truncate">
                            {widget.name}
                          </CardTitle>
                          <div className="text-xs text-muted-foreground mt-1">
                            {widget.defaultSize.w}×{widget.defaultSize.h} grid units
                          </div>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <CardDescription className="text-sm mb-4">
                        {widget.description}
                      </CardDescription>
                      <Button
                        size="sm"
                        className="w-full"
                        onClick={() => onAddWidget(widget.type, widget.name, widget.defaultSize)}
                      >
                        Add Widget
                      </Button>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
          </div>
        </div>
      ))}
    </div>
  );
}

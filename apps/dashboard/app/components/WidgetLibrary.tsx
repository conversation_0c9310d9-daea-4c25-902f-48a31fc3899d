"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import {
	FileText,
	CheckSquare,
	Clock,
	Calendar,
	BarChart3,
	Thermometer,
	Globe,
	Edit3,
	ChevronDown,
	ChevronUp,
} from "lucide-react";
import { <PERSON><PERSON> } from "./ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "./ui/card";
import { Badge } from "./ui/badge";
import { WidgetType } from "../types/grid";
import {
	getSupportedSizes,
	getDefaultSize,
	getSizeLabel,
} from "../config/widgetSizes";

interface WidgetDefinition {
	type: WidgetType;
	name: string;
	description: string;
	icon: React.ComponentType<{ className?: string }>;
	category: string;
}

interface WidgetLibraryProps {
	onAddWidget: (
		type: string,
		title: string,
		defaultSize: { w: number; h: number }
	) => void;
}

const widgetDefinitions: WidgetDefinition[] = [
	{
		type: "collaborative-editor",
		name: "Collaborative Editor",
		description: "Real-time collaborative text editor with shared tasks",
		icon: Edit3,
		category: "Productivity",
	},
	{
		type: "task-list",
		name: "Task List",
		description: "Simple task management with checkboxes",
		icon: CheckSquare,
		category: "Productivity",
	},
	{
		type: "notes",
		name: "Quick Notes",
		description: "Simple note-taking widget",
		icon: FileText,
		category: "Productivity",
	},
	{
		type: "clock",
		name: "Clock",
		description: "Digital clock with timezone support",
		icon: Clock,
		category: "Utilities",
	},
	{
		type: "calendar",
		name: "Calendar",
		description: "Monthly calendar view",
		icon: Calendar,
		category: "Utilities",
	},
	{
		type: "chart",
		name: "Chart",
		description: "Data visualization charts",
		icon: BarChart3,
		category: "Analytics",
	},
	{
		type: "weather",
		name: "Weather",
		description: "Current weather conditions",
		icon: Thermometer,
		category: "Utilities",
	},
	{
		type: "web-view",
		name: "Web View",
		description: "Embed web content",
		icon: Globe,
		category: "Utilities",
	},
];

const categories = Array.from(
	new Set(widgetDefinitions.map((w) => w.category))
);

export default function WidgetLibrary({ onAddWidget }: WidgetLibraryProps) {
	const [expandedWidgets, setExpandedWidgets] = useState<Set<string>>(
		new Set()
	);

	const toggleExpanded = (widgetType: string) => {
		const newExpanded = new Set(expandedWidgets);
		if (newExpanded.has(widgetType)) {
			newExpanded.delete(widgetType);
		} else {
			newExpanded.add(widgetType);
		}
		setExpandedWidgets(newExpanded);
	};

	return (
		<div className="space-y-6">
			{categories.map((category) => (
				<div key={category}>
					<h3 className="text-lg font-semibold text-foreground mb-3">
						{category}
					</h3>
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
						{widgetDefinitions
							.filter((widget) => widget.category === category)
							.map((widget) => {
								const supportedSizes = getSupportedSizes(widget.type);
								const defaultSize = getDefaultSize(widget.type);
								const isExpanded = expandedWidgets.has(widget.type);

								return (
									<motion.div
										key={widget.type}
										whileHover={{ scale: 1.02 }}
										whileTap={{ scale: 0.98 }}
									>
										<Card className="hover:shadow-md transition-shadow duration-200">
											<CardHeader className="pb-3">
												<div className="flex items-center gap-3">
													<div className="p-2 bg-primary/10 rounded-lg">
														<widget.icon className="h-5 w-5 text-primary" />
													</div>
													<div className="flex-1 min-w-0">
														<CardTitle className="text-sm font-medium truncate">
															{widget.name}
														</CardTitle>
														<div className="text-xs text-muted-foreground mt-1">
															{supportedSizes.length} size
															{supportedSizes.length !== 1 ? "s" : ""} available
														</div>
													</div>
													<Button
														variant="ghost"
														size="sm"
														className="h-6 w-6 p-0"
														onClick={() => toggleExpanded(widget.type)}
													>
														{isExpanded ? (
															<ChevronUp className="h-4 w-4" />
														) : (
															<ChevronDown className="h-4 w-4" />
														)}
													</Button>
												</div>
											</CardHeader>
											<CardContent className="pt-0">
												<CardDescription className="text-sm mb-4">
													{widget.description}
												</CardDescription>

												{isExpanded ? (
													<div className="space-y-3">
														<div className="text-xs font-medium text-muted-foreground mb-2">
															Available Sizes:
														</div>
														<div className="grid grid-cols-2 gap-2">
															{supportedSizes.map((size) => (
																<Button
																	key={`${size.w}x${size.h}`}
																	variant="outline"
																	size="sm"
																	className="h-auto p-2 flex flex-col items-center gap-1"
																	onClick={() =>
																		onAddWidget(widget.type, widget.name, {
																			w: size.w,
																			h: size.h,
																		})
																	}
																>
																	<div className="text-xs font-medium">
																		{size.w}×{size.h}
																	</div>
																	<Badge
																		variant="secondary"
																		className="text-xs px-1 py-0"
																	>
																		{size.label}
																	</Badge>
																</Button>
															))}
														</div>
													</div>
												) : (
													<Button
														size="sm"
														className="w-full"
														onClick={() =>
															onAddWidget(widget.type, widget.name, {
																w: defaultSize.w,
																h: defaultSize.h,
															})
														}
													>
														Add {getSizeLabel(defaultSize)} Widget
													</Button>
												)}
											</CardContent>
										</Card>
									</motion.div>
								);
							})}
					</div>
				</div>
			))}
		</div>
	);
}

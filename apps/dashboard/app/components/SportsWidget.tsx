import { useState, useEffect } from 'react'

interface Game {
  id: number
  homeTeam: string
  awayTeam: string
  score: string
  status: string
}

export function SportsWidget() {
  const [games, setGames] = useState<Game[]>([])

  useEffect(() => {
    // Simulating API call
    const mockGames: Game[] = [
      { id: 1, homeTeam: 'Lakers', awayTeam: 'Celtics', score: '98-95', status: 'Final' },
      { id: 2, homeTeam: 'Warriors', awayTeam: 'Nets', score: '110-102', status: '3rd Qtr' },
      { id: 3, homeTeam: 'Heat', awayTeam: 'Bucks', score: '0-0', status: '7:30 PM' },
    ]
    setGames(mockGames)
  }, [])

  return (
    <div className="space-y-2">
      {games.map((game) => (
        <div key={game.id} className="flex justify-between items-center text-sm">
          <span className="font-medium">{game.homeTeam} vs {game.awayTeam}</span>
          <span className="text-gray-600 dark:text-gray-400">{game.score} ({game.status})</span>
        </div>
      ))}
    </div>
  )
}


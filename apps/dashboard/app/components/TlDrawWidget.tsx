import { useState, useEffect } from "react";
import { Tldraw } from "tldraw";
import { useSyncDemo } from "@tldraw/sync";
import "tldraw/tldraw.css";

interface Game {
  id: number;
  homeTeam: string;
  awayTeam: string;
  score: string;
  status: string;
}

export function TlDrawWidget() {
  const [games, setGames] = useState<Game[]>([]);
  const store = useSyncDemo({ roomId: "tldraw-test-app2" });

  return (
    <div className="space-y-2" style={{ height: "300px" }}>
      <div
        style={{
          position: "fixed",
          inset: 0,
          width: "100%",
          height: "100%",
          marginLeft: "auto",
          marginRight: "auto",
        }}
      >
        <Tldraw store={store} />
      </div>
    </div>
  );
}

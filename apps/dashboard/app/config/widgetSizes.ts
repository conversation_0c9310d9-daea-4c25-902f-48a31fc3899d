import { WidgetSize, WidgetType } from "../types/grid";

// Define all available widget sizes with their categories
export const WIDGET_SIZES: Record<string, WidgetSize> = {
  // Mini sizes
  "1x1": { w: 1, h: 1, label: "Tiny", category: "Mini" },
  "2x1": { w: 2, h: 1, label: "Wide Mini", category: "Mini" },
  "1x2": { w: 1, h: 2, label: "Tall Mini", category: "Mini" },
  
  // Standard sizes
  "2x2": { w: 2, h: 2, label: "Compact", category: "Standard" },
  "3x2": { w: 3, h: 2, label: "Wide Standard", category: "Standard" },
  "2x3": { w: 2, h: 3, label: "Tall Standard", category: "Standard" },
  
  // Extended/Hero sizes
  "3x3": { w: 3, h: 3, label: "Large", category: "Extended" },
  "4x2": { w: 4, h: 2, label: "Ultra Wide", category: "Extended" },
  "4x3": { w: 4, h: 3, label: "Wide Hero", category: "Hero" },
  "4x4": { w: 4, h: 4, label: "Maximum", category: "Hero" },
};

// Define supported sizes for each widget type based on functional requirements
export const WIDGET_SUPPORTED_SIZES: Record<WidgetType, string[]> = {
  // Productivity widgets
  "collaborative-editor": ["2x2", "3x3", "4x3", "4x4"],
  "task-list": ["1x2", "2x2", "2x3"],
  "notes": ["1x1", "2x1", "2x2"],
  
  // Utilities widgets
  "clock": ["1x1", "2x1", "2x2"],
  "calendar": ["2x2", "3x2", "3x3"],
  "weather": ["1x1", "2x1", "2x2"],
  "web-view": ["2x2", "3x2", "2x3", "3x3", "4x3", "4x4"],
  
  // Analytics widgets
  "chart": ["2x2", "3x2", "2x3", "3x3", "4x2", "4x3"],
};

// Get supported sizes for a widget type
export function getSupportedSizes(widgetType: WidgetType): WidgetSize[] {
  const sizeKeys = WIDGET_SUPPORTED_SIZES[widgetType] || [];
  return sizeKeys.map(key => WIDGET_SIZES[key]).filter(Boolean);
}

// Get default size for a widget type (first supported size)
export function getDefaultSize(widgetType: WidgetType): WidgetSize {
  const supportedSizes = getSupportedSizes(widgetType);
  return supportedSizes[0] || WIDGET_SIZES["2x2"];
}

// Check if a size is supported for a widget type
export function isSizeSupported(widgetType: WidgetType, size: { w: number; h: number }): boolean {
  const supportedSizes = getSupportedSizes(widgetType);
  return supportedSizes.some(s => s.w === size.w && s.h === size.h);
}

// Get size category for display purposes
export function getSizeCategory(size: { w: number; h: number }): string {
  const sizeKey = `${size.w}x${size.h}`;
  return WIDGET_SIZES[sizeKey]?.category || "Custom";
}

// Get size label for display purposes
export function getSizeLabel(size: { w: number; h: number }): string {
  const sizeKey = `${size.w}x${size.h}`;
  return WIDGET_SIZES[sizeKey]?.label || `${size.w}×${size.h}`;
}

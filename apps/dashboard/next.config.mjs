let userConfig = undefined;
try {
	userConfig = await import("./v0-user-next.config");
} catch (e) {
	// ignore error
}

/** @type {import('next').NextConfig} */
import path from "path";

const nextConfig = {
	eslint: {
		ignoreDuringBuilds: true,
	},
	typescript: {
		ignoreBuildErrors: true,
	},
	images: {
		unoptimized: true,
	},
	experimental: {
		webpackBuildWorker: true,
		parallelServerBuildTraces: true,
		parallelServerCompiles: true,
	},
};

mergeConfig(nextConfig, userConfig);

function mergeConfig(nextConfig, userConfig) {
	if (!userConfig) {
		return;
	}

	for (const key in userConfig) {
		if (
			typeof nextConfig[key] === "object" &&
			!Array.isArray(nextConfig[key])
		) {
			nextConfig[key] = {
				...nextConfig[key],
				...userConfig[key],
			};
		} else {
			nextConfig[key] = userConfig[key];
		}
	}
}

nextConfig.webpack = (config) => {
	config.resolve = config.resolve || {};
	config.resolve.alias = config.resolve.alias || {};
	config.resolve.alias["@logo"] = path.resolve(
		process.cwd(),
		"app/components/Logo"
	);

	// Fix tldraw multiple instances issue
	config.resolve.alias = {
		...config.resolve.alias,
		tldraw: path.resolve(process.cwd(), "node_modules/tldraw"),
		"@tldraw/sync": path.resolve(process.cwd(), "node_modules/@tldraw/sync"),
		yjs: path.resolve(process.cwd(), "node_modules/yjs"),
		"@hocuspocus/provider": path.resolve(
			process.cwd(),
			"node_modules/@hocuspocus/provider"
		),
	};

	// Ensure proper ES module handling
	config.experiments = {
		...config.experiments,
		topLevelAwait: true,
	};

	return config;
};

export default nextConfig;

{"name": "ecosystem", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "check-types": "turbo run check-types", "dev:collab": "ts-node apps/collab-server/index.ts"}, "devDependencies": {"prettier": "^3.5.3", "ts-node": "^10.9.2", "turbo": "^2.5.2", "typescript": "5.8.2"}, "packageManager": "pnpm@9.0.0", "engines": {"node": ">=18"}, "dependencies": {"@hocuspocus/server": "^3.1.0", "y-protocols": "^1.0.6", "yjs": "^13.6.26"}}